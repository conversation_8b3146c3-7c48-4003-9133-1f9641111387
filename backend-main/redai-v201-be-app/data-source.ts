import { DataSource } from 'typeorm';
import { SnakeNamingStrategy } from 'typeorm-naming-strategies';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * DataSource configuration for TypeORM CLI
 * Sử dụng cho migration và các command line tools
 */
const AppDataSource = new DataSource({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  username: process.env.DB_USERNAME || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  database: process.env.DB_DATABASE || 'redai_db',
  ssl: process.env.DB_SSL === 'true' ? { rejectUnauthorized: false } : false,

  // Entity paths
  entities: [
    'src/**/*.entity.ts',
    'src/**/*.entity.js'
  ],

  // Migration paths
  migrations: [
    'src/database/migrations/*.ts',
    'src/database/migrations/*.js'
  ],

  // Subscriber paths
  subscribers: [
    'src/**/*.subscriber.ts',
    'src/**/*.subscriber.js'
  ],

  // Naming strategy
  namingStrategy: new SnakeNamingStrategy(),

  // Logging
  logging: true,
  logger: 'advanced-console',

  // Synchronize - NEVER use true in production
  synchronize: false,

  // Migration settings
  migrationsRun: false,
  migrationsTableName: 'migrations',
});

export default AppDataSource;
