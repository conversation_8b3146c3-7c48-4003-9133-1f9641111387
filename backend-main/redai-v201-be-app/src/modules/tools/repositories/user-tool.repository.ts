import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import { UserTool } from '../entities/user-tool.entity';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { PaginatedResult } from '@common/response/api-response-dto';

@Injectable()
export class UserToolRepository extends Repository<UserTool> {
  private readonly logger = new Logger(UserToolRepository.name);

  constructor(private dataSource: DataSource) {
    super(UserTool, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho user tool
   * @returns SelectQueryBuilder<UserTool> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<UserTool> {
    return this.createQueryBuilder('tool');
  }

  /**
   * Tìm tool theo ID
   * @param id ID của tool cần tìm
   * @param userId ID của người dùng (tù<PERSON> chọn, nếu cần kiểm tra quyền sở hữu)
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolById(id: string, userId?: number): Promise<UserTool | null> {
    const qb = this.createBaseQuery()
      .where('tool.id = :id', { id });

    if (userId) {
      qb.andWhere('tool.userId = :userId', { userId });
    }

    return qb.getOne();
  }

  /**
   * Tìm tool theo tên và ID người dùng
   * @param name Tên của tool cần tìm
   * @param userId ID của người dùng
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolByName(name: string, userId: number): Promise<UserTool | null> {
    return this.createBaseQuery()
      .where('tool.name = :name', { name })
      .andWhere('tool.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Lấy danh sách tool clone từ hệ thống với phân trang (chỉ tool có originalId)
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param userId ID của người dùng
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái tool (tùy chọn)
   * @param hasUpdate Có bản cập nhật mới (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tool clone từ hệ thống với phân trang
   */
  async findTools(
    page: number,
    limit: number,
    userId: number,
    search?: string,
    status?: ToolStatusEnum,
    hasUpdate?: boolean,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<UserTool>> {
    const qb = this.createBaseQuery()
      .where('tool.userId = :userId', { userId })
      .andWhere('tool.originalId IS NOT NULL'); // Chỉ lấy tool clone từ hệ thống

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('tool.status = :status', { status });
    }

    // Thêm điều kiện lọc theo hasUpdate nếu có
    if (hasUpdate !== undefined) {
      qb.andWhere('tool.hasUpdate = :hasUpdate', { hasUpdate });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`tool.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Kiểm tra xem danh sách tool_id của người dùng có tồn tại trong cơ sở dữ liệu không
   * @param toolIds Danh sách ID của các tool cần kiểm tra
   * @param userId ID của người dùng (tùy chọn, nếu cần kiểm tra quyền sở hữu)
   * @returns Đối tượng chứa thông tin về các ID tồn tại và không tồn tại
   */
  async validateToolIds(
    toolIds: string[],
    userId?: number
  ): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, trả về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Tạo query để tìm các tool có ID nằm trong danh sách
      const qb = this.createQueryBuilder('tool')
        .select('tool.id')
        .where('tool.id IN (:...toolIds)', { toolIds });

      // Nếu có userId, thêm điều kiện kiểm tra quyền sở hữu
      if (userId) {
        qb.andWhere('tool.userId = :userId', { userId });
      }

      // Thực hiện truy vấn
      const existingTools = await qb.getMany();

      // Lấy danh sách ID tồn tại
      const existingIds = existingTools.map((tool) => tool.id);

      // Lấy danh sách ID không tồn tại
      const nonExistingIds = toolIds.filter(
        (id) => !existingIds.includes(id),
      );

      return {
        valid: nonExistingIds.length === 0,
        existingIds,
        nonExistingIds,
      };
    } catch (error) {
      this.logger.error(`Error validating tool IDs: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }

  /**
   * Lấy tất cả các tool của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tất cả các tool của người dùng
   */
  async findAllByUserId(userId: number): Promise<UserTool[]> {
    return this.createBaseQuery()
      .where('tool.userId = :userId', { userId })
      .orderBy('tool.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Lấy danh sách tool của người dùng với phân trang
   * @param userId ID của người dùng
   * @param queryParams Tham số truy vấn và phân trang
   * @returns Danh sách tool với phân trang
   */
  async findAllByUserIdWithPagination(
    userId: number,
    queryParams: {
      page?: number;
      limit?: number;
      search?: string;
      sortBy?: string;
      sortDirection?: string;
    }
  ): Promise<PaginatedResult<UserTool>> {
    const { page = 1, limit = 10, search, sortBy = 'createdAt', sortDirection = 'desc' } = queryParams;
    const skip = (page - 1) * limit;

    // Tạo query builder
    const queryBuilder = this.createBaseQuery()
      .where('tool.userId = :userId', { userId });

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      queryBuilder.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)', { search: `%${search}%` });
    }

    // Áp dụng sắp xếp và phân trang
    queryBuilder
      .orderBy(`tool.${sortBy}`, sortDirection.toUpperCase() as 'ASC' | 'DESC')
      .skip(skip)
      .take(limit);

    // Thực hiện truy vấn
    const [items, totalItems] = await queryBuilder.getManyAndCount();

    // Trả về kết quả phân trang
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page
      }
    };
  }

  /**
   * Tìm tool của người dùng theo ID tool gốc từ admin
   * @param originalId ID của tool gốc từ admin
   * @param userId ID của người dùng
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolByOriginalId(originalId: string, userId: number): Promise<UserTool | null> {
    return this.createBaseQuery()
      .where('tool.originalId = :originalId', { originalId })
      .andWhere('tool.userId = :userId', { userId })
      .getOne();
  }

  /**
   * Tìm tất cả các tool của người dùng đã clone từ một admin tool
   * @param originalId ID của tool gốc từ admin
   * @returns Danh sách các tool đã clone
   */
  async findAllToolsByOriginalId(originalId: string): Promise<UserTool[]> {
    return this.createBaseQuery()
      .where('tool.originalId = :originalId', { originalId })
      .getMany();
  }

  /**
   * Lấy danh sách tool của người dùng có isUpdate=true
   * @param userId ID của người dùng
   * @returns Danh sách tool có isUpdate=true
   */
  async findToolsWithUpdate(userId: number): Promise<UserTool[]> {
    return this.createBaseQuery()
      .where('tool.userId = :userId', { userId })
      .andWhere('tool.isUpdate = :isUpdate', { isUpdate: true })
      .andWhere('tool.originalId IS NOT NULL')
      .getMany();
  }

  /**
   * Lấy danh sách tool đã được sao chép bởi người dùng
   * @param userId ID của người dùng
   * @returns Danh sách tool đã sao chép
   */
  async findClonedToolsByUser(userId: number): Promise<UserTool[]> {
    return this.createBaseQuery()
      .where('tool.userId = :userId', { userId })
      .andWhere('tool.originalId IS NOT NULL')
      .getMany();
  }

  /**
   * Tìm tool theo ID với thông tin group từ admin tool
   * @param id ID của tool cần tìm
   * @param userId ID của người dùng
   * @returns Tool với thông tin group nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolByIdWithGroups(id: string, userId: number): Promise<any> {
    return this.createQueryBuilder('tool')
      .leftJoin('admin_tools', 'adminTool', 'tool.originalId = adminTool.id')
      .leftJoin('admin_group_tool_mappings', 'groupMapping', 'adminTool.id = groupMapping.tool_id')
      .leftJoin('admin_group_tools', 'groupTool', 'groupMapping.group_id = groupTool.id')
      .select([
        'tool.id',
        'tool.name',
        'tool.description',
        'tool.createdAt',
        'tool.updatedAt',
        'tool.originalId',
        'tool.hasUpdate',
        'tool.status',
        'tool.active',
        'tool.isUpdate',
        'groupTool.id as groupId',
        'groupTool.name as groupName'
      ])
      .where('tool.id = :id', { id })
      .andWhere('tool.userId = :userId', { userId })
      .getRawMany();
  }

  /**
   * Lấy danh sách tool với thông tin group từ admin tool
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param userId ID của người dùng
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái tool (tùy chọn)
   * @param hasUpdate Có bản cập nhật mới (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tool với thông tin group
   */
  async findToolsWithGroups(
    page: number,
    limit: number,
    userId: number,
    search?: string,
    status?: ToolStatusEnum,
    hasUpdate?: boolean,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: any[], total: number }> {
    let qb = this.createQueryBuilder('tool')
      .leftJoin('admin_tools', 'adminTool', 'tool.originalId = adminTool.id')
      .leftJoin('admin_group_tool_mappings', 'groupMapping', 'adminTool.id = groupMapping.tool_id')
      .leftJoin('admin_group_tools', 'groupTool', 'groupMapping.group_id = groupTool.id')
      .select([
        'tool.id',
        'tool.name',
        'tool.description',
        'tool.createdAt',
        'tool.updatedAt',
        'tool.originalId',
        'tool.hasUpdate',
        'tool.status',
        'tool.active',
        'tool.isUpdate',
        'groupTool.id as groupId',
        'groupTool.name as groupName'
      ])
      .where('tool.userId = :userId', { userId })
      .andWhere('tool.originalId IS NOT NULL'); // Chỉ lấy tool clone từ hệ thống

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('tool.status = :status', { status });
    }

    // Thêm điều kiện lọc theo hasUpdate nếu có
    if (hasUpdate !== undefined) {
      qb.andWhere('tool.hasUpdate = :hasUpdate', { hasUpdate });
    }

    // Đếm tổng số records
    const countQb = this.createQueryBuilder('tool')
      .where('tool.userId = :userId', { userId })
      .andWhere('tool.originalId IS NOT NULL');

    if (search) {
      countQb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
        { search: `%${search}%` });
    }
    if (status) {
      countQb.andWhere('tool.status = :status', { status });
    }
    if (hasUpdate !== undefined) {
      countQb.andWhere('tool.hasUpdate = :hasUpdate', { hasUpdate });
    }

    const total = await countQb.getCount();

    // Thêm phân trang và sắp xếp
    qb.orderBy(`tool.${sortBy}`, sortDirection)
      .offset((page - 1) * limit)
      .limit(limit);

    const items = await qb.getRawMany();

    return { items, total };
  }
}
