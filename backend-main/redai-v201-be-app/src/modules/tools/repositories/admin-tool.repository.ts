import { Injectable, Logger } from '@nestjs/common';
import { DataSource, In, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminTool } from '../entities/admin-tool.entity';
import { ToolStatusEnum } from '../constants/tool-status.enum';
import { AccessTypeEnum } from '../constants/access-type.enum';
import { PaginatedResult } from '@common/response/api-response-dto';

@Injectable()
export class AdminToolRepository extends Repository<AdminTool> {
  private readonly logger = new Logger(AdminToolRepository.name);

  constructor(private dataSource: DataSource) {
    super(AdminTool, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho admin tool
   * @returns SelectQueryBuilder<AdminTool> để sử dụng trong các phương thức khác
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTool> {
    return this.createQueryBuilder('tool');
  }

  /**
   * Tìm tool theo ID
   * @param id ID của tool cần tìm
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolById(id: string): Promise<AdminTool | null> {
    return this.createBaseQuery()
      .where('tool.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm tool theo tên
   * @param name Tên của tool cần tìm
   * @returns Tool nếu tìm thấy, null nếu không tìm thấy
   */
  async findToolByName(name: string): Promise<AdminTool | null> {
    return this.createBaseQuery()
      .where('tool.name = :name', { name })
      .getOne();
  }

  /**
   * Lấy danh sách tool với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái tool (tùy chọn)
   * @param accessType Loại quyền truy cập (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách tool với phân trang
   */
  async findTools(
    page: number,
    limit: number,
    search?: string,
    status?: ToolStatusEnum,
    accessType?: AccessTypeEnum,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<AdminTool>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(tool.name ILIKE :search OR tool.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo trạng thái nếu có
    if (status) {
      qb.andWhere('tool.status = :status', { status });
    }

    // Thêm điều kiện lọc theo loại quyền truy cập nếu có
    if (accessType) {
      qb.andWhere('tool.accessType = :accessType', { accessType });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`tool.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Kiểm tra tính hợp lệ và tính duy nhất của functionName
   * @param toolName Tên function cần kiểm tra
   * @param excludeToolId ID của tool cần loại trừ khi kiểm tra (dùng khi update)
   * @returns Kết quả kiểm tra: { valid: boolean, reason?: string }
   */
  async validateFunctionName(
    toolName: string,
    excludeToolId?: string,
  ): Promise<{ valid: boolean; reason?: string }> {
    // Kiểm tra định dạng functionName theo quy tắc của OpenAI
    const nameRegex = /^[a-zA-Z0-9_]+$/;
    if (!nameRegex.test(toolName)) {
      return {
        valid: false,
        reason:
          'Tên hàm chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
      };
    }

    // Kiểm tra độ dài functionName
    if (toolName.length > 64) {
      return {
        valid: false,
        reason: 'Tên hàm không được vượt quá 64 ký tự',
      };
    }

    try {
      // Kiểm tra tính duy nhất của functionName trong codeDefinition
      const qb = this.createQueryBuilder('tool')
        .leftJoin(
          'admin_tool_versions',
          'version',
          'tool.id = version.tool_id',
        )
        .where('version.tool_name = :toolName', { toolName });

      // Nếu có excludeToolId, loại trừ tool này khỏi kết quả kiểm tra
      if (excludeToolId) {
        qb.andWhere('tool.id != :excludeToolId', { excludeToolId });
      }

      const existingTool = await qb.getOne();

      if (existingTool) {
        return {
          valid: false,
          reason: `Tên hàm '${toolName}' đã được sử dụng`,
        };
      }

      return { valid: true };
    } catch (error) {
      this.logger.error(`Error validating function name: ${error.message}`, error.stack);
      return {
        valid: false,
        reason: 'Lỗi khi kiểm tra tên hàm',
      };
    }
  }

  /**
   * Lấy tất cả các tool
   * @returns Danh sách tất cả các tool
   */
  async findAll(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .orderBy('tool.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Kiểm tra xem danh sách tool_id có tồn tại trong cơ sở dữ liệu không
   * @param toolIds Danh sách ID của các tool cần kiểm tra
   * @returns Đối tượng chứa thông tin về các ID tồn tại và không tồn tại
   */
  async validateToolIds(toolIds: string[]): Promise<{
    valid: boolean;
    existingIds: string[];
    nonExistingIds: string[];
  }> {
    try {
      // Nếu danh sách rỗng, trả về kết quả ngay lập tức
      if (!toolIds || toolIds.length === 0) {
        return {
          valid: false,
          existingIds: [],
          nonExistingIds: [],
        };
      }

      // Tìm tất cả các tool có ID nằm trong danh sách
      const existingTools = await this.find({
        where: { id: In(toolIds) },
        select: ['id'], // Chỉ lấy trường ID để tối ưu hiệu suất
      });

      // Lấy danh sách ID tồn tại
      const existingIds = existingTools.map((tool) => tool.id);

      // Lấy danh sách ID không tồn tại
      const nonExistingIds = toolIds.filter(
        (id) => !existingIds.includes(id),
      );

      return {
        valid: nonExistingIds.length === 0,
        existingIds,
        nonExistingIds,
      };
    } catch (error) {
      this.logger.error(`Error validating tool IDs: ${error.message}`, error.stack);
      return {
        valid: false,
        existingIds: [],
        nonExistingIds: toolIds,
      };
    }
  }

  /**
   * Lấy danh sách tất cả tool có trạng thái APPROVED
   * @returns Danh sách tool APPROVED
   */
  async findAllApprovedTools(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .where('tool.status = :status', { status: ToolStatusEnum.APPROVED })
      .getMany();
  }

  /**
   * Lấy danh sách tất cả tool công khai có trạng thái APPROVED
   * @returns Danh sách tool công khai APPROVED
   */
  async findAllPublicApprovedTools(): Promise<AdminTool[]> {
    return this.createBaseQuery()
      .where('tool.accessType = :accessType', { accessType: AccessTypeEnum.PUBLIC })
      .andWhere('tool.status = :status', { status: ToolStatusEnum.APPROVED })
      .getMany();
  }
}
