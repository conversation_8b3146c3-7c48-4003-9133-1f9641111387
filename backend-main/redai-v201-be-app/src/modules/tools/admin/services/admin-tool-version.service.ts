import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { AdminToolVersion } from '../../entities';
import {
  AdminToolRepository,
  AdminToolVersionRepository,
  UserToolRepository,
} from '../../repositories';
import {
  CreateToolVersionDto,
  EmployeeInfoDto,
  UpdateToolVersionDto,
  VersionDto,
} from '../dto';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import { Transactional } from 'typeorm-transactional';
import { ToolStatusEnum } from '@/modules/tools/constants';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';

@Injectable()
export class AdminToolVersionService {
  private readonly logger = new Logger(AdminToolVersionService.name);

  constructor(
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
    private readonly employeeInfoRepository: EmployeeInfoRepository,
    private readonly userToolRepository: UserToolRepository,
  ) {}

  /**
   * Tạo phiên bản mới cho tool
   * @param toolId ID của tool
   * @param employeeId ID của nhân viên tạo phiên bản
   * @param createDto Dữ liệu tạo phiên bản
   * @returns ID của phiên bản đã tạo
   */
  @Transactional()
  async createVersion(
    toolId: string,
    employeeId: number,
    createDto: CreateToolVersionDto,
  ): Promise<string> {
    try {
      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép tạo phiên bản mới
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tính hợp lệ của functionName
      const functionNameValidation =
        await this.adminToolRepository.validateFunctionName(
          createDto.toolName,
          toolId,
        );
      if (!functionNameValidation.valid) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
          functionNameValidation.reason,
        );
      }

      // Lấy số phiên bản mới nhất
      const latestVersionNumber =
        await this.adminToolVersionRepository.getLatestVersionNumber(
          toolId,
        );
      const newVersionNumber = latestVersionNumber + 1;

      // Tạo phiên bản mới
      const newVersion = new AdminToolVersion();
      newVersion.toolId = toolId;
      newVersion.versionNumber = newVersionNumber;
      newVersion.toolName = createDto.toolName;
      newVersion.toolDescription = createDto.toolDescription || null;
      newVersion.parameters = createDto.parameters;
      newVersion.changeDescription = createDto.changeDescription || null;
      newVersion.status = createDto.status || ToolStatusEnum.DRAFT;
      newVersion.createdBy = employeeId;
      newVersion.updatesBy = employeeId;

      // Lưu phiên bản
      const savedVersion =
        await this.adminToolVersionRepository.save(newVersion);

      // Cập nhật trạng thái "có update" cho tất cả các user tools đã clone từ tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(toolId);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Đánh dấu ${userTools.length} user tools có bản cập nhật mới từ admin tool ${toolId}`);

          for (const userTool of userTools) {
            userTool.hasUpdate = true;
            userTool.isUpdate = true;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);
          }
        }
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc tạo phiên bản
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return savedVersion.id;
    } catch (error) {
      this.logger.error(
        `Failed to create version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_CREATION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy thông tin phiên bản
   * @param versionId ID của phiên bản
   * @returns Thông tin phiên bản
   */
  async getVersionById(versionId: string): Promise<VersionDto> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Lấy thông tin người tạo
      const createdBy = await this.employeeInfoRepository.getEmployeeInfo(
        version.createdBy,
      );

      // Chuyển đổi sang DTO
      return this.mapVersionToDto(version, createdBy);
    } catch (error) {
      this.logger.error(
        `Failed to get version by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
        error.message,
      );
    }
  }

  /**
   * Cập nhật thông tin phiên bản
   * @param versionId ID của phiên bản cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của phiên bản đã cập nhật
   */
  @Transactional()
  async updateVersion(
    versionId: string,
    employeeId: number,
    updateDto: UpdateToolVersionDto,
  ): Promise<string> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(version.toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép cập nhật phiên bản
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tính hợp lệ của toolName (nếu có cập nhật)
      if (
        updateDto.toolName &&
        updateDto.toolName !== version.toolName
      ) {
        const functionNameValidation =
          await this.adminToolRepository.validateFunctionName(
            updateDto.toolName,
            version.toolId,
          );
        if (!functionNameValidation.valid) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
            functionNameValidation.reason,
          );
        }
      }

      // Cập nhật thông tin phiên bản
      if (updateDto.toolName) version.toolName = updateDto.toolName;
      if (updateDto.toolDescription !== undefined)
        version.toolDescription = updateDto.toolDescription;
      if (updateDto.parameters) version.parameters = updateDto.parameters;
      if (updateDto.changeDescription !== undefined)
        version.changeDescription = updateDto.changeDescription;
      if (updateDto.status) version.status = updateDto.status;

      version.updatesBy = employeeId;
      version.updatedAt = Date.now();

      // Lưu phiên bản
      await this.adminToolVersionRepository.save(version);

      // Cập nhật trạng thái "có update" cho tất cả các user tools đã clone từ tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(version.toolId);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Đánh dấu ${userTools.length} user tools có bản cập nhật mới từ admin tool ${version.toolId}`);

          for (const userTool of userTools) {
            userTool.hasUpdate = true;
            userTool.isUpdate = true;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);
          }
        }
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc cập nhật phiên bản
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return version.id;
    } catch (error) {
      this.logger.error(
        `Failed to update version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Đặt phiên bản làm mặc định
   * @param functionId ID của tool
   * @param versionId ID của phiên bản
   * @param employeeId ID của nhân viên
   * @returns true nếu thành công
   */
  @Transactional()
  async setDefaultVersion(
    toolId: string,
    versionId: string,
    employeeId: number,
  ): Promise<boolean> {
    try {
      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(toolId);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép đặt phiên bản mặc định
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra phiên bản có tồn tại không và thuộc về tool không
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version || version.toolId !== toolId) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra trạng thái của phiên bản
      if (version.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_VERSION_INVALID,
          'Không thể đặt phiên bản đã bị xóa làm mặc định',
        );
      }

      // Cập nhật thông tin tool
      tool.updatedBy = employeeId;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.adminToolRepository.save(tool);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to set default version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Gỡ phiên bản (xóa mềm)
   * @param versionId ID của phiên bản cần gỡ
   * @param employeeId ID của nhân viên
   * @returns true nếu thành công
   */
  @Transactional()
  async removeVersion(versionId: string, employeeId: number): Promise<boolean> {
    try {
      // Lấy thông tin phiên bản
      const version =
        await this.adminToolVersionRepository.findVersionById(versionId);
      if (!version) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND);
      }

      // Kiểm tra tool có tồn tại không
      const tool = await this.adminToolRepository.findToolById(
        version.toolId,
      );
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép xóa phiên bản
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra xem phiên bản này có phải là phiên bản mặc định không
      // Không cần kiểm tra versionDefault nữa vì đã loại bỏ trường này
      /*if (tool.versionDefault === versionId) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_VERSION_DELETE_FAILED,
          'Không thể gỡ phiên bản mặc định. Vui lòng đặt phiên bản khác làm mặc định trước khi gỡ.',
        );
      }*/

      // Cập nhật trạng thái phiên bản thành DEPRECATED
      version.status = ToolStatusEnum.DEPRECATED;
      version.updatesBy = employeeId;
      version.updatedAt = Date.now();

      // Lưu phiên bản
      await this.adminToolVersionRepository.save(version);

      return true;
    } catch (error) {
      this.logger.error(
        `Failed to remove version: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_VERSION_DELETE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @param createdBy Thông tin người tạo
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(
    version: AdminToolVersion,
    createdBy: EmployeeInfoDto,
  ): VersionDto {
    const versionDto = new VersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName;
    versionDto.versionNumber = version.versionNumber; // Giữ lại để tương thích ngược
    versionDto.toolName = version.toolName;
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.changeDescription = version.changeDescription;
    versionDto.status = version.status;
    versionDto.createdAt = version.createdAt;
    versionDto.createdBy = createdBy;
    return versionDto;
  }
}
