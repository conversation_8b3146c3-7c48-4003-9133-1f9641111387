import {
  AccessType<PERSON>num,
  ToolStatusEnum,
} from '@/modules/tools/constants';
import { EmployeeInfoRepository } from '@/modules/tools/repositories/employee-info.repository';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response/api-response-dto';
import { Injectable, Logger } from '@nestjs/common';
import { S3Service } from '@shared/services/s3.service';
import { Transactional } from 'typeorm-transactional';
import { AdminTool, AdminToolVersion } from '../../entities';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  AdminGroupToolMappingRepository,
  AdminToolRepository,
  AdminToolVersionRepository,
  UserGroupToolMappingRepository,
  UserToolRepository,
  UserToolVersionRepository,
} from '../../repositories';
import {
  CreateToolDto,
  EmployeeInfoDto,
  QueryToolDto,
  SimpleVersionDto,
  ToolDetailDto,
  ToolListItemDto,
  UpdateToolDto,
  VersionDto,
} from '../dto';

@Injectable()
export class AdminToolService {
  private readonly logger = new Logger(AdminToolService.name);

  constructor(
    private readonly adminToolRepository: AdminToolRepository,
    private readonly adminToolVersionRepository: AdminToolVersionRepository,
    private readonly employeeInfoRepository: EmployeeInfoRepository,
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
    private readonly adminGroupToolMappingRepository: AdminGroupToolMappingRepository,
    private readonly userGroupToolMappingRepository: UserGroupToolMappingRepository,
  ) { }

  /**
   * Tạo mới tool và phiên bản đầu tiên
   * @param employeeId ID của nhân viên tạo tool
   * @param createDto Dữ liệu tạo tool
   * @returns ID của tool đã tạo
   */
  @Transactional()
  async createTool(
    employeeId: number,
    createDto: CreateToolDto,
  ): Promise<string> {
    try {
      // Kiểm tra tên hiển thị của tool đã tồn tại chưa
      const existingTool = await this.adminToolRepository.findToolByName(
        createDto.name,
      );

      if (existingTool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
      }

      // Kiểm tra tính hợp lệ của toolName
      const functionNameValidation =
        await this.adminToolRepository.validateFunctionName(
          createDto.toolName,
        );
      if (!functionNameValidation.valid) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
          functionNameValidation.reason,
        );
      }

      // Tạo tool mới
      const newTool = new AdminTool();
      newTool.name = createDto.name;
      newTool.description = createDto.description || null;
      newTool.status = createDto.status || ToolStatusEnum.DRAFT;
      newTool.accessType = createDto.accessType || AccessTypeEnum.PUBLIC;
      newTool.createdBy = employeeId;
      newTool.updatedBy = employeeId;

      // Lưu tool
      const savedTool = await this.adminToolRepository.save(newTool);

      // Tạo phiên bản đầu tiên
      const newVersion = new AdminToolVersion();
      newVersion.toolId = savedTool.id;
      newVersion.versionName = createDto.versionName || 'v1.0.0'; // Sử dụng versionName từ DTO hoặc giá trị mặc định
      newVersion.toolName = createDto.toolName;
      newVersion.toolDescription = createDto.toolDescription || null;
      newVersion.parameters = createDto.parameters;
      newVersion.changeDescription = createDto.changeDescription || 'Phiên bản đầu tiên';
      newVersion.status = createDto.status || ToolStatusEnum.DRAFT;
      newVersion.createdBy = employeeId;
      newVersion.updatesBy = employeeId;

      // Lưu phiên bản
      await this.adminToolVersionRepository.save(newVersion);

      return savedTool.id;
    } catch (error) {
      this.logger.error(`Failed to create tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_CREATION_FAILED,
        error.message,
      );
    }
  }

  /**
   * Lấy danh sách tool với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách tool với phân trang
   */
  async getTools(
    queryDto: QueryToolDto,
  ): Promise<PaginatedResult<ToolListItemDto>> {
    try {
      const { page, limit, search, status, accessType, sortBy, sortDirection } =
        queryDto;

      // Lấy danh sách tool từ repository
      const result = await this.adminToolRepository.findTools(
        page,
        limit,
        search,
        status,
        accessType,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi sang DTO
      const items = await Promise.all(
        result.items.map(async (tool) => {
          // Lấy phiên bản mới nhất của tool
          let latestVersion: AdminToolVersion | null = null;
          try {
            latestVersion = await this.adminToolVersionRepository.findLatestVersionByFunctionId(tool.id);
          } catch (error) {
            this.logger.warn(`Không thể lấy phiên bản mới nhất cho tool ${tool.id}: ${error.message}`);
          }

          const dto = new ToolListItemDto();
          dto.id = tool.id;
          dto.name = tool.name;
          dto.description = tool.description;
          dto.accessType = tool.accessType;
          dto.status = tool.status;
          dto.createdAt = tool.createdAt;
          dto.updatedAt = tool.updatedAt;

          // Không còn sử dụng trường groupId và group
          dto.groupId = null;
          dto.groupName = null;

          // Thêm thông tin về phiên bản mặc định
          if (latestVersion) {
            dto.versionDefault = latestVersion.id;
          }

          return dto;
        }),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Failed to get tools: ${error.message}`, error.stack);
      throw new AppException(TOOLS_ERROR_CODES.FETCH_FAILED, error.message);
    }
  }

  /**
   * Lấy thông tin chi tiết tool
   * @param id ID của tool
   * @returns Thông tin chi tiết tool
   */
  async getToolById(id: string): Promise<ToolDetailDto> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép xem chi tiết
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Lấy thông tin người tạo và người cập nhật
      const createdBy = await this.employeeInfoRepository.getEmployeeInfo(
        tool.createdBy,
      );
      const updatedBy = await this.employeeInfoRepository.getEmployeeInfo(
        tool.updatedBy,
      );

      // Lấy danh sách phiên bản
      const versions =
        await this.adminToolVersionRepository.findVersionsByToolId(id);

      // Lấy phiên bản mới nhất
      let defaultVersion: AdminToolVersion | null = null;
      if (versions.length > 0) {
        defaultVersion = versions[0]; // Lấy phiên bản đầu tiên (mới nhất) vì đã sắp xếp theo createdAt DESC
      }

      // Chuyển đổi phiên bản sang DTO đơn giản (chỉ ID và tên phiên bản)
      const simpleVersionDtos = versions.map(version => {
        const dto = new SimpleVersionDto();
        dto.id = version.id;
        dto.versionName = version.versionName;
        return dto;
      });

      // Chuyển đổi phiên bản mặc định sang DTO đầy đủ
      let defaultVersionDto: VersionDto | null = null;
      if (defaultVersion) {
        const defaultVersionCreatedBy =
          await this.employeeInfoRepository.getEmployeeInfo(
            defaultVersion.createdBy,
          );
        defaultVersionDto = this.mapVersionToDto(
          defaultVersion,
          defaultVersionCreatedBy,
        );
      }

      // Tạo DTO chi tiết
      const detailDto = new ToolDetailDto();
      detailDto.id = tool.id;
      detailDto.name = tool.name;
      detailDto.description = tool.description;
      detailDto.accessType = tool.accessType;
      detailDto.status = tool.status;
      detailDto.createdAt = tool.createdAt;
      detailDto.updatedAt = tool.updatedAt;
      detailDto.versionName = defaultVersion?.versionName || null;
      detailDto.createdBy = createdBy;
      detailDto.updatedBy = updatedBy;
      detailDto.defaultVersion = defaultVersionDto;
      detailDto.versions = simpleVersionDtos;

      return detailDto;
    } catch (error) {
      this.logger.error(
        `Failed to get tool by ID: ${error.message}`,
        error.stack,
      );
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND, error.message);
    }
  }

  /**
   * Cập nhật thông tin tool
   * @param id ID của tool cần cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @param updateDto Dữ liệu cập nhật
   * @returns ID của tool đã cập nhật
   */
  @Transactional()
  async updateTool(
    id: string,
    employeeId: number,
    updateDto: UpdateToolDto,
  ): Promise<string> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đã bị xóa (DEPRECATED), không cho phép sửa
      if (tool.status === ToolStatusEnum.DEPRECATED) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_AVAILABLE,
          'Tool này đã bị xóa và không còn khả dụng.'
        );
      }

      // Kiểm tra tên hiển thị của tool đã tồn tại chưa (nếu có cập nhật tên)
      if (updateDto.name && updateDto.name !== tool.name) {
        const existingTool = await this.adminToolRepository.findToolByName(
          updateDto.name,
        );
        if (existingTool && existingTool.id !== id) {
          throw new AppException(TOOLS_ERROR_CODES.TOOL_ALREADY_EXISTS);
        }
      }

      // Cập nhật thông tin tool
      if (updateDto.name) tool.name = updateDto.name;
      if (updateDto.description !== undefined)
        tool.description = updateDto.description;
      if (updateDto.status) tool.status = updateDto.status;
      if (updateDto.accessType) tool.accessType = updateDto.accessType;

      tool.updatedBy = employeeId;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.adminToolRepository.save(tool);

      // Cập nhật trạng thái "có update" cho tất cả các user tools đã clone từ tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(id);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Đánh dấu ${userTools.length} user tools có bản cập nhật mới từ admin tool ${id}`);

          for (const userTool of userTools) {
            userTool.hasUpdate = true;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);
          }
        }
      } catch (updateError) {
        // Ghi log lỗi nhưng không ảnh hưởng đến việc cập nhật admin tool
        this.logger.error(`Lỗi khi cập nhật trạng thái cho user tools: ${updateError.message}`, updateError.stack);
      }

      return tool.id;
    } catch (error) {
      this.logger.error(`Failed to update tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Xóa tool
   * @param id ID của tool cần xóa
   * @param employeeId ID của nhân viên xóa
   * @returns true nếu xóa thành công
   */
  @Transactional()
  async deleteTool(id: string, employeeId: number): Promise<boolean> {
    try {
      // Kiểm tra nếu id là 'groups', trả về lỗi rõ ràng
      if (id === 'groups') {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
          'Để truy cập nhóm tool, vui lòng sử dụng /v1/admin/tools/groups'
        );
      }

      // Lấy thông tin tool
      const tool = await this.adminToolRepository.findToolById(id);
      if (!tool) {
        throw new AppException(TOOLS_ERROR_CODES.TOOL_NOT_FOUND);
      }

      // Kiểm tra nếu tool đang ở trạng thái isForSale = true thì không cho phép xóa
      if (tool.isForSale) {
        throw new AppException(
          TOOLS_ERROR_CODES.TOOL_DELETE_FAILED,
          'Không thể xóa tool đang được bán trên marketplace. Vui lòng gỡ tool khỏi marketplace trước khi xóa.'
        );
      }

      // Kiểm tra xem tool có đang được gán vào group tool nào không
      const groupToolMappings = await this.adminGroupToolMappingRepository.findByToolId(id);
      if (groupToolMappings && groupToolMappings.length > 0) {
        // Gỡ tool khỏi tất cả các group tool
        this.logger.log(`Gỡ tool ${id} khỏi ${groupToolMappings.length} nhóm tool trước khi xóa`);

        // Lấy danh sách tên nhóm để log
        const groupNames = groupToolMappings.map(mapping =>
          mapping.group ? `"${mapping.group.name}" (ID: ${mapping.groupId})` : `ID: ${mapping.groupId}`
        ).join(', ');
        this.logger.log(`Tool ${id} đang được gán vào các nhóm: ${groupNames}`);

        // Xóa tất cả các mapping liên quan đến tool này
        await this.adminGroupToolMappingRepository.deleteByToolId(id);
      }

      // Cập nhật trạng thái thành DEPRECATED
      tool.status = ToolStatusEnum.DEPRECATED;
      tool.updatedBy = employeeId;
      tool.updatedAt = Date.now();

      // Lưu tool
      await this.adminToolRepository.save(tool);

      // Vô hiệu hóa tất cả các user tools đã clone từ admin tool này
      try {
        const userTools = await this.userToolRepository.findAllToolsByOriginalId(id);
        if (userTools && userTools.length > 0) {
          this.logger.log(`Vô hiệu hóa ${userTools.length} user tools đã clone từ admin tool ${id}`);

          for (const userTool of userTools) {
            // Đánh dấu user tool là DEPRECATED
            userTool.status = ToolStatusEnum.DEPRECATED;
            userTool.updatedAt = Date.now();
            await this.userToolRepository.save(userTool);

            // Gỡ user tool khỏi tất cả các user group tool
            try {
              const userGroupToolMappings = await this.userGroupToolMappingRepository.findByToolId(userTool.id);
              if (userGroupToolMappings && userGroupToolMappings.length > 0) {
                this.logger.log(`Gỡ user tool ${userTool.id} khỏi ${userGroupToolMappings.length} nhóm tool của người dùng`);

                // Lấy danh sách ID nhóm để log
                const groupIds = userGroupToolMappings.map(mapping => mapping.groupId).join(', ');
                this.logger.log(`User tool ${userTool.id} đang được gán vào các nhóm có ID: ${groupIds}`);

                // Xóa tất cả các mapping liên quan đến user tool này
                await this.userGroupToolMappingRepository.deleteByToolId(userTool.id);
              }
            } catch (mappingError) {
              this.logger.error(`Lỗi khi gỡ user tool ${userTool.id} khỏi các nhóm: ${mappingError.message}`, mappingError.stack);
            }

            // Xóa tất cả các phiên bản của user tool
            try {
              await this.userToolVersionRepository.createQueryBuilder()
                .delete()
                .from('user_tool_versions')
                .where('original_function_id = :toolId', { toolId: userTool.id })
                .execute();
              this.logger.log(`Đã xóa tất cả các phiên bản của user tool ${userTool.id}`);
            } catch (versionError) {
              this.logger.error(`Lỗi khi xóa các phiên bản của user tool ${userTool.id}: ${versionError.message}`, versionError.stack);
            }
          }
        }
      } catch (userToolError) {
        this.logger.error(`Lỗi khi vô hiệu hóa user tools: ${userToolError.message}`, userToolError.stack);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to delete tool: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        TOOLS_ERROR_CODES.TOOL_DELETE_FAILED,
        error.message,
      );
    }
  }

  /**
   * Chuyển đổi phiên bản sang DTO
   * @param version Phiên bản
   * @param createdBy Thông tin người tạo
   * @returns DTO của phiên bản
   */
  private mapVersionToDto(
    version: AdminToolVersion,
    createdBy: EmployeeInfoDto,
  ): VersionDto {
    const versionDto = new VersionDto();
    versionDto.id = version.id;
    versionDto.versionName = version.versionName;
    versionDto.versionNumber = version.versionNumber; // Giữ lại để tương thích ngược
    versionDto.toolName = version.toolName;
    versionDto.toolDescription = version.toolDescription;
    versionDto.parameters = version.parameters;
    versionDto.changeDescription = version.changeDescription;
    versionDto.status = version.status;
    versionDto.createdAt = version.createdAt;
    versionDto.createdBy = createdBy;
    return versionDto;
  }
}
