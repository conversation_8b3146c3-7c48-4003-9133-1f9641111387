import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, Matches, MaxLength, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ToolStatusEnum } from '../../constants/tool-status.enum';
import { AccessTypeEnum } from '../../constants/access-type.enum';

/**
 * DTO cho tham số của tool
 */
export class ParameterDto {
  @ApiProperty({
    description: 'Loại tham số',
    example: 'object',
  })
  @IsString()
  @IsNotEmpty()
  type: string;

  @ApiProperty({
    description: 'Thuộc tính của tham số',
    example: {
      query: {
        type: 'string',
        description: 'Câu truy vấn tìm kiếm',
      },
    },
  })
  @IsObject()
  @IsOptional()
  properties?: Record<string, any>;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> tham số bắt buộc',
    example: ['query'],
    required: false,
  })
  @IsOptional()
  required?: string[];

  @ApiProperty({
    description: 'Mô tả tham số',
    example: 'Tham số đầu vào cho công cụ tìm kiếm',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;
}

/**
 * DTO cho việc tạo mới tool
 */
export class CreateToolDto {
  @ApiProperty({
    description: 'Tên hiển thị của tool',
    example: 'Công cụ tìm kiếm',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  @ApiProperty({
    description: 'Mô tả về tool',
    example: 'Công cụ giúp tìm kiếm thông tin từ nhiều nguồn',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Tên tool trong định nghĩa code (chỉ chứa a-z, A-Z, 0-9, _)',
    example: 'searchTool',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z0-9_]+$/, {
    message: 'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
  })
  @MaxLength(64)
  toolName: string;

  @ApiProperty({
    description: 'Mô tả chi tiết về chức năng của tool',
    example: 'Tìm kiếm thông tin từ nhiều nguồn dữ liệu khác nhau',
    required: false,
  })
  @IsString()
  @IsOptional()
  toolDescription?: string;

  @ApiProperty({
    description: 'Tham số của tool',
    type: ParameterDto,
  })
  @ValidateNested()
  @Type(() => ParameterDto)
  @IsNotEmpty()
  parameters: ParameterDto;

  @ApiProperty({
    description: 'Trạng thái của tool',
    enum: ToolStatusEnum,
    default: ToolStatusEnum.DRAFT,
    required: false,
  })
  @IsEnum(ToolStatusEnum)
  @IsOptional()
  status?: ToolStatusEnum = ToolStatusEnum.DRAFT;

  @ApiProperty({
    description: 'Loại quyền truy cập',
    enum: AccessTypeEnum,
    default: AccessTypeEnum.PUBLIC,
    required: false,
  })
  @IsEnum(AccessTypeEnum)
  @IsOptional()
  accessType?: AccessTypeEnum = AccessTypeEnum.PUBLIC;

  @ApiProperty({
    description: 'ID của nhóm tool',
    example: 1,
    required: false,
  })
  @IsOptional()
  groupId: number;

  @ApiProperty({
    description: 'Mô tả những thay đổi cho phiên bản đầu tiên',
    example: 'Phiên bản đầu tiên',
    required: false,
  })
  @IsString()
  @IsOptional()
  changeDescription?: string;

  @ApiProperty({
    description: 'Tên phiên bản đầu tiên',
    example: 'v1.0.0',
    required: false,
  })
  @IsString()
  @IsOptional()
  versionName?: string;
}
