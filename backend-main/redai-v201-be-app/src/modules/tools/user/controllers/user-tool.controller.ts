import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { ToolListItemDto } from '@/modules/tools/admin/dto';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';
import { CurrentUser } from '@modules/auth/decorators';
import { JwtUserGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { TOOLS_ERROR_CODES } from '../../exceptions';
import {
  EditToolVersionDto,
  QueryUserToolDto,
  RollbackToAdminVersionDto,
  UpdateFromAdminDto,
  UserToolDetailDto
} from '../dto';
import { UserToolService } from '../services';

@ApiTags(SWAGGER_API_TAGS.USER_TOOL)
@Controller('user/tools')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class UserToolController {
  constructor(private readonly userToolService: UserToolService) { }

  @Get()
  @ApiOperation({ summary: 'Lấy danh sách tool của người dùng' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách tool của người dùng',
    type: () => ApiResponseDto<PaginatedResult<ToolListItemDto>>,
  })
  @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  async getUserTools(
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryUserToolDto,
  ) {
    const result = await this.userToolService.getUserTools(userId, queryDto);
    return ApiResponseDto.paginated(result);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết tool của người dùng' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết tool của người dùng',
    type: () => ApiResponseDto.success(UserToolDetailDto),
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async getUserToolById(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    const result = await this.userToolService.getUserToolById(id, userId);
    return ApiResponseDto.success(result);
  }



  @Post('update-from-admin')
  @ApiOperation({ summary: 'Cập nhật tool từ phiên bản mới của admin' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async updateFromAdmin(
    @CurrentUser('id') userId: number,
    @Body() updateDto: UpdateFromAdminDto,
  ) {
    const toolId = await this.userToolService.updateFromAdmin(
      userId,
      updateDto,
    );
    return ApiResponseDto.success({ id: toolId });
  }

  @Post(':id/versions/:versionId/edit')
  @ApiOperation({ summary: 'Chỉnh sửa phiên bản tool' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiParam({ name: 'versionId', description: 'ID của phiên bản' })
  @ApiResponse({
    status: 200,
    description: 'Phiên bản đã được chỉnh sửa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED,
    TOOLS_ERROR_CODES.TOOL_PARAMETERS_INVALID
  )
  async editToolVersion(
    @Param('id') toolId: string,
    @Param('versionId') versionId: string,
    @CurrentUser('id') userId: number,
    @Body() editDto: EditToolVersionDto,
  ) {
    const newVersionId = await this.userToolService.editToolVersion(
      userId,
      toolId,
      versionId,
      editDto,
    );
    return ApiResponseDto.success({ id: newVersionId });
  }

  @Post('clone-all')
  @ApiOperation({ summary: 'Sao chép tất cả tool có trạng thái APPROVED và loại PUBLIC với tiền tố userId_toolname' })
  @ApiResponse({
    status: 200,
    description: 'Số lượng tool đã sao chép',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_CREATION_FAILED
  )
  async cloneAllTools(
    @CurrentUser('id') userId: number,
  ) {
    const count = await this.userToolService.cloneAllTools(userId);
    return ApiResponseDto.success({ count });
  }

  @Post('rollback-to-admin')
  @ApiOperation({ summary: 'Khôi phục về phiên bản gốc từ admin' })
  @ApiResponse({
    status: 200,
    description: 'Tool đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_VERSION_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_ACCESS_DENIED
  )
  async rollbackToAdminVersion(
    @CurrentUser('id') userId: number,
    @Body() rollbackDto: RollbackToAdminVersionDto,
  ) {
    const toolId = await this.userToolService.rollbackToAdminVersion(
      userId,
      rollbackDto,
    );
    return ApiResponseDto.success({ id: toolId });
  }

  @Put(':id/active')
  @ApiOperation({ summary: 'Bật/tắt trạng thái active của tool (đảo ngược trạng thái hiện tại)' })
  @ApiParam({ name: 'id', description: 'ID của tool' })
  @ApiResponse({
    status: 200,
    description: 'Trạng thái active đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    ErrorCode.INTERNAL_SERVER_ERROR,
    TOOLS_ERROR_CODES.TOOL_NOT_FOUND,
    TOOLS_ERROR_CODES.TOOL_UPDATE_FAILED
  )
  async toggleToolActive(
    @Param('id') id: string,
    @CurrentUser('id') userId: number,
  ) {
    await this.userToolService.toggleToolActive(id, userId);
    return ApiResponseDto.success({ message: 'Trạng thái active đã được cập nhật thành công' });
  }
}
