import { Injectable, Logger } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { CUSTOM_TOOLS_ERROR_CODES, TOOLS_ERROR_CODES } from '../../exceptions';
import { <PERSON><PERSON><PERSON>ey, OAuth, UserTool, UserToolVersion, UserToolsCustom } from '../../entities';
import {
  ApiKeyRepository,
  OAuthRepository,
  UserToolRepository,
  UserToolVersionRepository,
  UserToolsCustomRepository
} from '../../repositories';
import { ApiKeyLocationEnum, HttpMethodEnum, TokenSourceEnum, ToolStatusEnum } from '../../constants';
import { Transactional } from 'typeorm-transactional';
import { RouteType, RouteMap } from '../dto/integrate-openapi.dto';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryDto } from '@common/dto';
import { ApiKeyAuthDto, AuthTypeEnum, OAuthAuthDto } from '../dto/auth-config.dto';
import { IntegrateFromOpenApiDto } from '../dto/integrate-from-openapi.dto';
import { ToolDetailResponseDto, ToolResponseDto } from '../dto/tool-response.dto';
import { UpdateToolAuthDto } from '../dto/update-tool-auth.dto';
import { UpdateBaseUrlDto } from '../dto/update-base-url.dto';

/**
 * Interface cho route HTTP
 */
interface HTTPRoute {
  method: string;              // Phương thức HTTP (GET, POST, v.v.)
  path: string;                // Đường dẫn của route (ví dụ: "/users")
  operation_id?: string;       // ID của operation (tùy chọn)
  description?: string;        // Mô tả của route (tùy chọn)
  summary?: string;            // Tóm tắt của route (tùy chọn)
  parameters?: any[];          // Danh sách tham số (tùy chọn)
  security?: any[];            // Thông tin xác thực (tùy chọn)
  servers?: {                  // Danh sách server (tùy chọn)
    url: string;               // URL của server
    description?: string;      // Mô tả của server (tùy chọn)
  }[];
  responses?: Record<string, any>; // Thông tin responses (tùy chọn)
  openapiSpec?: Record<string, any>; // Đặc tả OpenAPI đầy đủ (để giải quyết tham chiếu)
}

/**
 * Interface cho thông tin xác thực từ OpenAPI
 */
interface SecurityScheme {
  type: string;                // Loại xác thực (apiKey, oauth2, http, openIdConnect)
  name?: string;               // Tên tham số (cho apiKey)
  in?: string;                 // Vị trí (cho apiKey: header, query, cookie)
  scheme?: string;             // Scheme (cho http: bearer, basic)
  bearerFormat?: string;       // Định dạng bearer (cho http với scheme là bearer)
  flows?: any;                 // Flows (cho oauth2)
  openIdConnectUrl?: string;   // URL OpenID Connect (cho openIdConnect)
  description?: string;        // Mô tả
}

/**
 * Service quản lý tích hợp công cụ từ OpenAPI
 */
@Injectable()
export class IntegrationToolsService {
  private readonly logger = new Logger(IntegrationToolsService.name);

  constructor(
    private readonly userToolRepository: UserToolRepository,
    private readonly userToolVersionRepository: UserToolVersionRepository,
    private readonly userToolsCustomRepository: UserToolsCustomRepository,
    private readonly apiKeyRepository: ApiKeyRepository,
    private readonly oAuthRepository: OAuthRepository,
  ) {}

  /**
   * Tích hợp công cụ từ đặc tả OpenAPI (phương thức gộp xử lý cả có auth và không auth)
   * @param userId ID của người dùng
   * @param integrateDto Dữ liệu tích hợp (có thể có hoặc không có auth)
   * @returns Kết quả tích hợp
   */
  @Transactional()
  async integrateFromOpenApi(
    userId: number,
    integrateDto: IntegrateFromOpenApiDto
  ): Promise<{
    toolsCreated: number,
    resourcesCreated: number,
    failedEndpoints: Array<{path: string, method: string, reason: string}>,
    authConfig?: {
      apiKeyCreated: number,
      oauthCreated: number
    }
  }> {
    try {
      // Code cứng quy tắc ánh xạ: Tất cả các phương thức đều là Tool
      const hardcodedMappings: RouteMap[] = [
        { methods: ["GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD"], pattern: ".*", route_type: RouteType.TOOL }
      ];

      const httpRoutes = this.parseOpenApiToHttpRoutes(integrateDto.openapiSpec);

      // Trích xuất thông tin xác thực từ OpenAPI
      const securitySchemes = this.extractSecuritySchemes(integrateDto.openapiSpec);

      // Xác định loại xác thực
      let authConfig: ApiKeyAuthDto | OAuthAuthDto | any = null;
      if ('authConfig' in integrateDto && integrateDto.authConfig) {
        authConfig = integrateDto.authConfig;
      }

      let toolsCreated = 0;
      let resourcesCreated = 0;
      let apiKeyCreated = 0;
      let oauthCreated = 0;
      const failedEndpoints: Array<{path: string, method: string, reason: string}> = [];

      // Xử lý từng route
      for (const route of httpRoutes) {
        try {
          // Luôn sử dụng hardcodedMappings (cấu hình ngầm)
          const componentType = this.determineComponentType(route, hardcodedMappings);

          // Xác định thông tin xác thực cho route
          const securityInfo = authConfig ?
            this.getSecurityInfoForRoute(route, securitySchemes, authConfig) : null;

          if (componentType === RouteType.TOOL) {
            // Truyền baseUrl từ DTO nếu có
            const toolInfo = this.extractToolInfo(route, integrateDto.baseUrl);

            // Nếu có thông tin xác thực, tạo tool với xác thực
            if (securityInfo) {
              const customTool = await this.createCustomToolWithAuth(userId, toolInfo, securityInfo);

              if (customTool) {
                toolsCreated++;

                // Đếm số lượng cấu hình xác thực đã tạo
                if (customTool.apiKeyId) {
                  apiKeyCreated++;
                } else if (customTool.oauthId) {
                  oauthCreated++;
                }
              }
            } else {
              // Không có thông tin xác thực, tạo tool thông thường
              await this.createToolFromOpenApi(userId, toolInfo);
              toolsCreated++;
            }
          } else if (componentType === RouteType.RESOURCE) {
            const resourceInfo = this.extractResourceInfo(route);
            await this.createResourceFromOpenApi(userId, resourceInfo);
            resourcesCreated++;
          }
        } catch (error) {
          // Ghi log lỗi nhưng không dừng quá trình
          this.logger.error(`Lỗi khi xử lý endpoint ${route.method} ${route.path}: ${error.message}`, error.stack);

          // Thêm vào danh sách endpoint lỗi
          failedEndpoints.push({
            path: route.path,
            method: route.method,
            reason: error.message
          });

          // Tiếp tục với endpoint tiếp theo
          continue;
        }
      }

      // Trả về kết quả phù hợp với loại đầu vào
      const result: any = {
        toolsCreated,
        resourcesCreated,
        failedEndpoints
      };

      if (authConfig) {
        result.authConfig = {
          apiKeyCreated,
          oauthCreated
        };
      }

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi tích hợp từ OpenAPI: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(TOOLS_ERROR_CODES.TOOL_CREATION_FAILED, error.message);
    }
  }

  /**
   * Xác định loại thành phần từ route
   * @param route Route HTTP
   * @param mappings Quy tắc ánh xạ
   * @returns Loại route (TOOL hoặc RESOURCE)
   */
  private determineComponentType(route: HTTPRoute, mappings: RouteMap[]): RouteType {
    for (const routeMap of mappings) {
      if (routeMap.methods.includes(route.method.toUpperCase())) {
        const pattern = routeMap.pattern;
        if (typeof pattern === 'string') {
          if (new RegExp(pattern).test(route.path)) {
            return routeMap.route_type;
          }
        } else if (pattern instanceof RegExp) {
          if (pattern.test(route.path)) {
            return routeMap.route_type;
          }
        }
      }
    }
    return RouteType.TOOL; // Mặc định là TOOL nếu không khớp
  }

  /**
   * Trích xuất thông tin cho Tool
   * @param route Route HTTP
   * @param customBaseUrl Base URL tùy chỉnh từ DTO (nếu có)
   * @returns Thông tin tool
   */
  private extractToolInfo(route: HTTPRoute, customBaseUrl?: string): Record<string, any> {
    const operationId = route.operation_id ||
      `${route.method.toLowerCase()}_${route.path.replace(/\//g, '_').replace(/^{|}$/g, '')}`;
    const description = route.description ||
      route.summary ||
      `Thực thi ${route.method} ${route.path}`;
    // Đảm bảo parameters là một mảng
    const parameters = Array.isArray(route.parameters) ? route.parameters : [];

    // Ưu tiên sử dụng baseUrl từ DTO nếu có
    // Nếu không, trích xuất từ servers trong đặc tả OpenAPI
    const baseUrl = customBaseUrl || (route.servers && route.servers.length > 0
      ? route.servers[0].url
      : null);

    return {
      name: operationId,
      description: description,
      parameters: parameters,
      method: route.method,
      path: route.path,
      type: "TOOL",
      baseUrl: baseUrl,
      responses: route.responses,
      openapiSpec: route.openapiSpec
    };
  }

  /**
   * Trích xuất thông tin cho Resource
   * @param route Route HTTP
   * @returns Thông tin resource
   */
  private extractResourceInfo(route: HTTPRoute): Record<string, any> {
    const operationId = route.operation_id ||
      `${route.method.toLowerCase()}_${route.path.replace(/\//g, '_').replace(/^{|}$/g, '')}`;
    const uri = `resource://${operationId}`;
    const description = route.description ||
      route.summary ||
      `Đại diện cho ${route.path}`;

    return {
      uri: uri,
      name: operationId,
      description: description,
      method: route.method,
      path: route.path,
      type: "RESOURCE"
    };
  }

  /**
   * Phân tích đặc tả OpenAPI thành danh sách HTTPRoute
   * @param openapiSpec Đặc tả OpenAPI
   * @returns Danh sách route HTTP
   */
  private parseOpenApiToHttpRoutes(openapiSpec: Record<string, any>): HTTPRoute[] {
    const routes: HTTPRoute[] = [];

    // Kiểm tra xem openapiSpec có hợp lệ không
    if (!openapiSpec || !openapiSpec.paths) {
      this.logger.warn('Đặc tả OpenAPI không hợp lệ: thiếu thuộc tính paths');
      return routes;
    }

    try {
      for (const path in openapiSpec.paths) {
        for (const method in openapiSpec.paths[path]) {
          try {
            // Bỏ qua các thuộc tính không phải phương thức HTTP
            if (!['get', 'post', 'put', 'delete', 'patch', 'options', 'head'].includes(method.toLowerCase())) {
              continue;
            }

            const route = openapiSpec.paths[path][method];

            // Tổng hợp tham số từ nhiều nguồn
            let allParameters: any[] = [];

            // Thêm tham số từ parameters ở cấp path
            if (Array.isArray(openapiSpec.paths[path].parameters)) {
              allParameters = [...allParameters, ...openapiSpec.paths[path].parameters];
            }

            // Thêm tham số từ parameters ở cấp operation
            if (Array.isArray(route.parameters)) {
              allParameters = [...allParameters, ...route.parameters];
            }

            // Thêm tham số từ requestBody nếu có
            if (route.requestBody && route.requestBody.content) {
              const contentTypes = Object.keys(route.requestBody.content);
              if (contentTypes.length > 0) {
                // Ưu tiên application/json nếu có
                const preferredContentType = contentTypes.includes('application/json')
                  ? 'application/json'
                  : contentTypes[0];

                const schema = route.requestBody.content[preferredContentType].schema;
                const examples = route.requestBody.content[preferredContentType].examples || {};
                const example = route.requestBody.content[preferredContentType].example;

                if (schema) {
                  // Nếu schema là tham chiếu
                  if (schema.$ref) {
                    // Xử lý tham chiếu để liên kết đúng tới DTO
                    const refPath = schema.$ref.split('/');
                    const refName = refPath.pop();

                    // Tìm schema từ components
                    const refSchema = this.resolveSchemaRef(openapiSpec, schema.$ref);

                    if (refSchema) {
                      const bodyParam = {
                        name: 'body',
                        in: 'body',
                        description: route.requestBody.description || `Request body (${refName})`,
                        required: route.requestBody.required || false,
                        schema: refSchema
                      };

                      // Thêm examples nếu có
                      if (Object.keys(examples).length > 0) {
                        bodyParam['examples'] = examples;
                      } else if (example) {
                        bodyParam['example'] = example;
                      }

                      allParameters.push(bodyParam);
                    } else {
                      // Nếu không tìm thấy schema, sử dụng tham chiếu gốc
                      allParameters.push({
                        name: 'body',
                        in: 'body',
                        description: route.requestBody.description || `Request body (${refName})`,
                        required: route.requestBody.required || false,
                        schema: schema
                      });
                    }
                  }
                  // Nếu schema là object trực tiếp
                  else if (schema.properties) {
                    // Tạo một tham số body duy nhất với toàn bộ schema
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                  // Nếu schema là array
                  else if (schema.type === 'array') {
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body (array)',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                  // Các loại schema khác
                  else {
                    const bodyParam = {
                      name: 'body',
                      in: 'body',
                      description: route.requestBody.description || 'Request body',
                      required: route.requestBody.required || false,
                      schema: schema
                    };

                    // Thêm examples nếu có
                    if (Object.keys(examples).length > 0) {
                      bodyParam['examples'] = examples;
                    } else if (example) {
                      bodyParam['example'] = example;
                    }

                    allParameters.push(bodyParam);
                  }
                }
              }
            }

            // Lấy thông tin servers từ route hoặc từ openapiSpec
            const routeServers = route.servers || openapiSpec.servers;

            // Lấy thông tin responses để thêm vào schema
            const responses = route.responses || {};

            // Tạo mô tả chi tiết hơn
            let detailedDescription = '';
            if (route.description) {
              detailedDescription += route.description + '\n\n';
            } else if (route.summary) {
              detailedDescription += route.summary + '\n\n';
            }

            // Thêm thông tin về tham số
            if (allParameters.length > 0) {
              detailedDescription += 'Tham số:\n';
              allParameters.forEach(param => {
                detailedDescription += `- ${param.name} (${param.in}): ${param.description || 'Không có mô tả'}\n`;
              });
              detailedDescription += '\n';
            }

            // Thêm thông tin về responses
            if (Object.keys(responses).length > 0) {
              detailedDescription += 'Responses:\n';
              for (const statusCode in responses) {
                const response = responses[statusCode];
                detailedDescription += `- ${statusCode}: ${response.description || 'Không có mô tả'}\n`;
              }
            }

            routes.push({
              method: method.toUpperCase(),
              path: path,
              operation_id: route.operationId,
              description: detailedDescription.trim() || route.description,
              summary: route.summary,
              parameters: allParameters,
              security: route.security || [],
              servers: routeServers,
              responses: responses,
              openapiSpec: openapiSpec // Thêm toàn bộ đặc tả OpenAPI để sử dụng khi giải quyết tham chiếu
            });
          } catch (error) {
            // Ghi log lỗi nhưng tiếp tục xử lý các route khác
            this.logger.error(`Lỗi khi xử lý route ${method.toUpperCase()} ${path}: ${error.message}`, error.stack);
            // Không throw lỗi để tiếp tục xử lý các route khác
          }
        }
      }
    } catch (error) {
      this.logger.error(`Lỗi khi phân tích đặc tả OpenAPI: ${error.message}`, error.stack);
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.OPENAPI_SCHEMA_EXTRACTION_FAILED, `Lỗi khi phân tích đặc tả OpenAPI: ${error.message}`);
    }

    return routes;
  }

  /**
   * Giải quyết tham chiếu schema
   * @param openapiSpec Đặc tả OpenAPI
   * @param ref Đường dẫn tham chiếu
   * @returns Schema đã giải quyết hoặc null nếu không tìm thấy
   */
  private resolveSchemaRef(openapiSpec: Record<string, any>, ref: string): any {
    try {
      // Tham chiếu có dạng "#/components/schemas/User"
      const refPath = ref.replace('#/', '').split('/');

      // Duyệt qua các phần của đường dẫn để tìm schema
      let schema = openapiSpec;
      for (const part of refPath) {
        if (!schema[part]) {
          return null;
        }
        schema = schema[part];
      }

      // Nếu schema tìm thấy cũng có $ref, tiếp tục giải quyết
      if (schema.$ref) {
        return this.resolveSchemaRef(openapiSpec, schema.$ref);
      }

      return schema;
    } catch (error) {
      this.logger.warn(`Không thể giải quyết tham chiếu ${ref}: ${error.message}`);
      return null;
    }
  }

  /**
   * Trích xuất thông tin xác thực từ đặc tả OpenAPI
   * @param openapiSpec Đặc tả OpenAPI
   * @returns Đối tượng chứa thông tin xác thực
   */
  private extractSecuritySchemes(openapiSpec: Record<string, any>): Record<string, SecurityScheme> {
    const securitySchemes: Record<string, SecurityScheme> = {};

    // Kiểm tra xem có components và securitySchemes không
    if (!openapiSpec.components || !openapiSpec.components.securitySchemes) {
      return securitySchemes;
    }

    // Trích xuất thông tin từ securitySchemes
    for (const schemeName in openapiSpec.components.securitySchemes) {
      const scheme = openapiSpec.components.securitySchemes[schemeName];
      securitySchemes[schemeName] = scheme;
    }

    return securitySchemes;
  }

  /**
   * Lấy thông tin xác thực cho route
   * @param route Route HTTP
   * @param securitySchemes Thông tin xác thực từ OpenAPI
   * @param authConfig Cấu hình xác thực từ người dùng
   * @returns Thông tin xác thực cho route
   */
  private getSecurityInfoForRoute(
    route: HTTPRoute,
    securitySchemes: Record<string, SecurityScheme>,
    authConfig?: ApiKeyAuthDto | OAuthAuthDto | any
  ): any {
    // Nếu không có thông tin xác thực trong route hoặc không có securitySchemes
    if (!route.security || route.security.length === 0 || Object.keys(securitySchemes).length === 0) {
      // Nếu có authConfig từ người dùng, sử dụng nó
      if (authConfig) {
        return authConfig;
      }
      return null;
    }

    // Lấy thông tin xác thực đầu tiên từ route
    const firstSecurity = route.security[0];
    const securityName = Object.keys(firstSecurity)[0];

    if (!securityName || !securitySchemes[securityName]) {
      // Nếu có authConfig từ người dùng, sử dụng nó
      if (authConfig) {
        return authConfig;
      }
      return null;
    }

    const scheme = securitySchemes[securityName];

    // Nếu có authConfig từ người dùng và schemeName khớp, sử dụng nó
    if (authConfig && authConfig.schemeName === securityName) {
      return authConfig;
    }

    // Tạo thông tin xác thực dựa trên scheme
    if (scheme.type === 'apiKey') {
      return {
        authType: AuthTypeEnum.API_KEY,
        schemeName: securityName,
        apiKeyLocation: scheme.in === 'header' ? ApiKeyLocationEnum.HEADER : ApiKeyLocationEnum.QUERY,
        paramName: scheme.name || 'X-API-KEY',
        apiKey: '' // Cần người dùng cung cấp
      };
    } else if (scheme.type === 'oauth2' || scheme.type === 'http' && scheme.scheme === 'bearer') {
      return {
        authType: AuthTypeEnum.OAUTH,
        schemeName: securityName,
        tokenSource: TokenSourceEnum.JWT,
        token: '', // Cần người dùng cung cấp
        flowType: scheme.type === 'oauth2' ? Object.keys(scheme.flows)[0] : 'bearer',
        scopes: scheme.type === 'oauth2' ? Object.keys(scheme.flows[Object.keys(scheme.flows)[0]].scopes).join(' ') : ''
      };
    }

    // Nếu không hỗ trợ loại xác thực này
    return null;
  }

  /**
   * Tạo công cụ từ thông tin OpenAPI
   * @param userId ID của người dùng
   * @param toolInfo Thông tin công cụ
   */
  private async createToolFromOpenApi(userId: number, toolInfo: Record<string, any>): Promise<void> {
    // Kiểm tra xem tool đã tồn tại chưa
    const existingTool = await this.userToolRepository.findOne({
      where: {
        userId,
        name: toolInfo.name
      }
    });

    if (existingTool) {
      this.logger.log(`Tool ${toolInfo.name} đã tồn tại, bỏ qua`);
      return;
    }

    // Tạo tool mới
    const newTool = new UserTool();
    newTool.name = toolInfo.name;
    newTool.description = toolInfo.description;
    newTool.userId = userId;
    newTool.status = ToolStatusEnum.APPROVED;

    const savedTool = await this.userToolRepository.save(newTool);

    // Tạo phiên bản đầu tiên
    const newVersion = new UserToolVersion();
    newVersion.userId = userId;
    newVersion.originalToolId = savedTool.id;
    newVersion.toolName = toolInfo.name.replace(/\s+/g, '_').toLowerCase();
    newVersion.toolDescription = toolInfo.description;

    try {
      // Đảm bảo parameters là một mảng hoặc đối tượng hợp lệ trước khi định dạng
      newVersion.parameters = this.formatParameters(toolInfo.parameters);
    } catch (error) {
      this.logger.error(`Lỗi khi định dạng tham số cho tool ${toolInfo.name}: ${error.message}`);
      // Sử dụng đối tượng rỗng nếu có lỗi
      newVersion.parameters = {
        type: "object",
        properties: {},
        required: []
      };
    }

    newVersion.status = ToolStatusEnum.APPROVED;

    await this.userToolVersionRepository.save(newVersion);
  }

  /**
   * Tạo resource từ thông tin OpenAPI
   * @param userId ID của người dùng
   * @param resourceInfo Thông tin resource
   */
  private async createResourceFromOpenApi(userId: number, resourceInfo: Record<string, any>): Promise<void> {
    // Kiểm tra xem resource đã tồn tại chưa
    const existingTool = await this.userToolRepository.findOne({
      where: {
        userId,
        name: resourceInfo.name
      }
    });

    if (existingTool) {
      this.logger.log(`Resource ${resourceInfo.name} đã tồn tại, bỏ qua`);
      return;
    }

    // Tạo tool mới (resource cũng được lưu dưới dạng tool)
    const newTool = new UserTool();
    newTool.name = resourceInfo.name;
    newTool.description = resourceInfo.description;
    newTool.userId = userId;
    newTool.status = ToolStatusEnum.APPROVED;

    const savedTool = await this.userToolRepository.save(newTool);

    // Tạo phiên bản đầu tiên
    const newVersion = new UserToolVersion();
    newVersion.userId = userId;
    newVersion.originalToolId = savedTool.id;
    newVersion.toolName = resourceInfo.name.replace(/\s+/g, '_').toLowerCase();
    newVersion.toolDescription = resourceInfo.description;
    newVersion.parameters = {
      type: "object",
      properties: {
        uri: {
          type: "string",
          description: "URI của resource",
          default: resourceInfo.uri
        }
      }
    };
    newVersion.status = ToolStatusEnum.APPROVED;

    await this.userToolVersionRepository.save(newVersion);
  }

  /**
   * Định dạng responses cho phù hợp với định dạng của tool
   * @param responses Responses từ OpenAPI
   * @param openapiSpec Đặc tả OpenAPI đầy đủ (để giải quyết tham chiếu)
   * @returns Responses đã định dạng
   */
  private formatResponses(responses: Record<string, any>, openapiSpec?: Record<string, any>): Record<string, any> {
    const formattedResponses: Record<string, any> = {};

    // Xử lý từng status code
    for (const statusCode in responses) {
      const response = responses[statusCode];
      const formattedResponse: Record<string, any> = {
        description: response.description || `Response ${statusCode}`
      };

      // Xử lý schema nếu có
      if (response.content) {
        const contentTypes = Object.keys(response.content);
        if (contentTypes.length > 0) {
          // Ưu tiên application/json nếu có
          const preferredContentType = contentTypes.includes('application/json')
            ? 'application/json'
            : contentTypes[0];

          const schema = response.content[preferredContentType].schema;
          if (schema) {
            // Kiểm tra xem schema có phải là tham chiếu không
            if (schema.$ref && openapiSpec) {
              // Giải quyết tham chiếu
              const resolvedSchema = this.resolveSchemaRef(openapiSpec, schema.$ref);
              if (resolvedSchema) {
                // Sử dụng schema đã giải quyết
                formattedResponse.schema = resolvedSchema;
              } else {
                // Nếu không thể giải quyết, bỏ qua tham chiếu
                this.logger.warn(`Không thể giải quyết tham chiếu ${schema.$ref} trong response`);
              }
            } else {
              formattedResponse.schema = schema;
            }
          }

          // Thêm examples nếu có
          const examples = response.content[preferredContentType].examples;
          if (examples) {
            formattedResponse.examples = examples;
          }

          const example = response.content[preferredContentType].example;
          if (example) {
            formattedResponse.example = example;
          }
        }
      } else if (response.type && response.properties) {
        // Trường hợp response là một schema trực tiếp (không có content)
        formattedResponse.schema = response;
      }

      formattedResponses[statusCode] = formattedResponse;
    }

    // Log để debug
    this.logger.debug('Formatted responses:', formattedResponses);

    return {
      type: "object",
      properties: formattedResponses
    };
  }

  /**
   * Định dạng tham số cho phù hợp với định dạng mới của tool
   * @param parameters Tham số từ OpenAPI
   * @param openapiSpec Đặc tả OpenAPI đầy đủ (để giải quyết tham chiếu)
   * @returns Đối tượng chứa queryParam, pathParam và body đã phân loại
   */
  private formatParameters(parameters: any, openapiSpec?: Record<string, any>): {
    queryParam: Record<string, unknown>,
    pathParam: Record<string, unknown>,
    body: Record<string, unknown>
  } {
    // Đảm bảo parameters là một mảng
    const paramArray = Array.isArray(parameters) ? parameters : [];

    // Khởi tạo các đối tượng kết quả với định dạng mới
    const queryParam: Record<string, any> = {};
    const pathParam: Record<string, any> = {};
    const body: Record<string, any> = {};

    // Khởi tạo mảng required cho mỗi loại tham số
    const queryRequired: string[] = [];
    const pathRequired: string[] = [];
    const bodyRequired: string[] = [];

    // Log để debug
    this.logger.debug('Formatting parameters:', paramArray);

    // Nếu không có tham số, trả về các đối tượng rỗng
    if (paramArray.length === 0) {
      this.logger.debug('No parameters to format, returning empty objects');
      return {
        queryParam: { require: [] },
        pathParam: { require: [] },
        body: { require: [] }
      };
    }

    // Phân loại tham số theo vị trí (in)
    for (const param of paramArray) {
      if (!param || !param.name) {
        this.logger.warn('Tham số không hợp lệ, thiếu thuộc tính name:', param);
        continue;
      }

      // Xử lý schema chi tiết hơn
      let paramSchema: Record<string, any> = {
        type: param.schema?.type || "string",
        description: param.description || `Tham số ${param.name}`
      };

      // Thêm example nếu có
      if (param.example !== undefined) {
        paramSchema.description += `; example: ${param.example}`;
      } else if (param.schema?.example !== undefined) {
        paramSchema.description += `; example: ${param.schema.example}`;
      }

      // Kiểm tra nếu tham số là bắt buộc
      if (param.required === true) {
        if (param.in === 'query') {
          queryRequired.push(param.name);
        } else if (param.in === 'path') {
          pathRequired.push(param.name);
        } else if (param.in === 'body' || param.in === 'formData') {
          bodyRequired.push(param.name);
        }
      }

      // Nếu có schema chi tiết, sử dụng nó
      if (param.schema) {
        // Kiểm tra xem schema có phải là tham chiếu không
        if (param.schema.$ref && openapiSpec) {
          // Giải quyết tham chiếu
          const resolvedSchema = this.resolveSchemaRef(openapiSpec, param.schema.$ref);
          if (resolvedSchema) {
            // Sử dụng schema đã giải quyết
            if (resolvedSchema.type) paramSchema.type = resolvedSchema.type;

            // Thêm example từ schema đã giải quyết nếu có
            if (resolvedSchema.example !== undefined && !paramSchema.description.includes('example:')) {
              paramSchema.description += `; example: ${resolvedSchema.example}`;
            }

            // Nếu schema có properties và là object, xử lý các trường con
            if (resolvedSchema.type === 'object' && resolvedSchema.properties) {
              // Xử lý các trường con của object
              const requiredProps = resolvedSchema.required || [];

              // Tạo schema cho mỗi property
              for (const propName in resolvedSchema.properties) {
                const propSchema = resolvedSchema.properties[propName];
                const isRequired = requiredProps.includes(propName);

                // Tạo mô tả cho property
                let propDescription = propSchema.description || `${propName} property`;
                if (propSchema.example !== undefined) {
                  propDescription += `; example: ${propSchema.example}`;
                }

                // Thêm property vào body schema
                if (param.in === 'body' || param.in === 'formData') {
                  body[propName] = {
                    type: propSchema.type || 'string',
                    description: propDescription
                  };

                  // Nếu property là bắt buộc, thêm vào mảng required
                  if (isRequired) {
                    bodyRequired.push(propName);
                  }
                }
              }

              // Bỏ qua tham số gốc vì đã xử lý các trường con
              continue;
            }
          } else {
            // Nếu không thể giải quyết, ghi log và tiếp tục
            this.logger.warn(`Không thể giải quyết tham chiếu ${param.schema.$ref}`);
          }
        } else {
          // Sao chép các thuộc tính từ schema
          if (param.schema.type) paramSchema.type = param.schema.type;

          // Nếu schema là object và có properties
          if (param.schema.type === 'object' && param.schema.properties) {
            // Xử lý các trường con của object
            const requiredProps = param.schema.required || [];

            // Tạo schema cho mỗi property
            for (const propName in param.schema.properties) {
              const propSchema = param.schema.properties[propName];
              const isRequired = requiredProps.includes(propName);

              // Tạo mô tả cho property
              let propDescription = propSchema.description || `${propName} property`;
              if (propSchema.example !== undefined) {
                propDescription += `; example: ${propSchema.example}`;
              }

              // Thêm property vào body schema
              if (param.in === 'body' || param.in === 'formData') {
                body[propName] = {
                  type: propSchema.type || 'string',
                  description: propDescription
                };

                // Nếu property là bắt buộc, thêm vào mảng required
                if (isRequired) {
                  bodyRequired.push(propName);
                }
              }
            }

            // Bỏ qua tham số gốc vì đã xử lý các trường con
            continue;
          }
        }
      }

      // Phân loại tham số dựa trên vị trí (in)
      if (param.in === 'query') {
        queryParam[param.name] = paramSchema;
      } else if (param.in === 'path') {
        pathParam[param.name] = paramSchema;
      } else if (param.in === 'body') {
        // Nếu là body nhưng không có properties, lưu toàn bộ schema
        if (!param.schema || !param.schema.properties) {
          body[param.name] = paramSchema;
        }
      } else if (param.in === 'formData') {
        // Form data cũng được xem như body
        body[param.name] = paramSchema;
      } else {
        // Các loại khác (header, cookie) tạm thời lưu vào query
        queryParam[param.name] = paramSchema;
      }
    }

    // Thêm mảng require vào các đối tượng kết quả
    if (queryRequired.length > 0) {
      queryParam.require = queryRequired;
    } else {
      queryParam.require = [];
    }

    if (pathRequired.length > 0) {
      pathParam.require = pathRequired;
    } else {
      pathParam.require = [];
    }

    if (bodyRequired.length > 0) {
      body.require = bodyRequired;
    } else {
      body.require = [];
    }

    // Log để debug
    this.logger.debug('Path parameters:', pathParam);
    this.logger.debug('Query parameters:', queryParam);
    this.logger.debug('Body parameters:', body);

    // Đảm bảo các đối tượng không rỗng để tránh lưu null vào cơ sở dữ liệu
    return {
      queryParam: Object.keys(queryParam).length > 0 ? queryParam : { require: [] },
      pathParam: Object.keys(pathParam).length > 0 ? pathParam : { require: [] },
      body: Object.keys(body).length > 0 ? body : { require: [] }
    };
  }

  /**
   * Tạo công cụ tùy chỉnh với xác thực
   * @param userId ID của người dùng
   * @param toolInfo Thông tin công cụ
   * @param securityInfo Thông tin xác thực
   * @returns Công cụ tùy chỉnh đã tạo
   */
  @Transactional()
  private async createCustomToolWithAuth(
    userId: number,
    toolInfo: Record<string, any>,
    securityInfo: any
  ): Promise<UserToolsCustom | null> {
    try {
      // Kiểm tra xem công cụ đã tồn tại chưa
      const existingTool = await this.userToolsCustomRepository.findOne({
        where: {
          userId,
          toolName: toolInfo.name
        }
      });

      if (existingTool) {
        this.logger.log(`Công cụ tùy chỉnh ${toolInfo.name} đã tồn tại, bỏ qua`);
        return null;
      }

      // Tạo công cụ tùy chỉnh mới
      const newTool = new UserToolsCustom();
      newTool.userId = userId;
      newTool.toolName = toolInfo.name;
      newTool.toolDescription = toolInfo.description;
      newTool.endpoint = toolInfo.path;
      newTool.method = toolInfo.method as HttpMethodEnum;

      // Phân loại tham số vào các trường mới
      // Đảm bảo truyền openapiSpec để xử lý đúng các tham số
      let queryParam: Record<string, any> = { require: [] };
      let pathParam: Record<string, any> = { require: [] };
      let body: Record<string, any> = { require: [] };

      // Xử lý tham số từ parameters
      const paramResult = this.formatParameters(toolInfo.parameters, toolInfo.openapiSpec);
      queryParam = paramResult.queryParam;
      pathParam = paramResult.pathParam;
      body = paramResult.body;

      // Xử lý requestBody nếu có
      if (toolInfo.requestBody) {
        this.logger.debug('Found requestBody in toolInfo:', toolInfo.requestBody);
        // Gộp requestBody vào body
        body = { ...body, ...toolInfo.requestBody };
      }

      // Log để debug
      this.logger.debug('Query parameters:', queryParam);
      this.logger.debug('Path parameters:', pathParam);
      this.logger.debug('Body parameters:', body);

      newTool.queryParam = queryParam;
      newTool.pathParam = pathParam;
      newTool.body = body;

      // Lưu thông tin response nếu có
      if (toolInfo.responses) {
        newTool.response = this.formatResponses(toolInfo.responses, toolInfo.openapiSpec);
      }

      newTool.status = ToolStatusEnum.APPROVED;

      // Đặt giá trị cho baseUrl từ thông tin servers trong đặc tả OpenAPI
      if (toolInfo.baseUrl) {
        newTool.baseUrl = toolInfo.baseUrl;
      } else {
        // Nếu không có baseUrl trong toolInfo, sử dụng giá trị mặc định
        // Người dùng có thể thay đổi giá trị này sau bằng API updateBaseUrl
        newTool.baseUrl = 'http://localhost:8080';
        this.logger.warn(`Không tìm thấy baseUrl cho tool ${toolInfo.name}, sử dụng giá trị mặc định: ${newTool.baseUrl}. Người dùng có thể thay đổi sau bằng API updateBaseUrl.`);
      }

      // Xử lý thông tin xác thực
      if (securityInfo) {
        if (securityInfo.authType === AuthTypeEnum.API_KEY) {
          // Tạo cấu hình API Key
          const apiKey = new ApiKey();
          apiKey.schemeName = securityInfo.schemeName || 'apiKey';
          apiKey.apiKey = securityInfo.apiKey || '';
          apiKey.apiKeyLocation = securityInfo.apiKeyLocation;
          apiKey.paramName = securityInfo.paramName || 'X-API-KEY';

          const savedApiKey = await this.apiKeyRepository.createApiKey(apiKey);
          newTool.apiKeyId = savedApiKey.id;
        } else if (securityInfo.authType === AuthTypeEnum.OAUTH) {
          // Tạo cấu hình OAuth
          const oauth = new OAuth();
          oauth.schemeName = securityInfo.schemeName || 'oauth2';
          oauth.token = securityInfo.token || '';
          oauth.tokenSource = securityInfo.tokenSource;
          oauth.clientId = securityInfo.clientId;
          oauth.clientSecret = securityInfo.clientSecret;
          oauth.authUrl = securityInfo.authUrl;
          oauth.tokenUrl = securityInfo.tokenUrl;
          oauth.refreshUrl = securityInfo.refreshUrl;
          oauth.refreshToken = securityInfo.refreshToken;
          oauth.scopes = securityInfo.scopes;
          oauth.flowType = securityInfo.flowType;

          const savedOAuth = await this.oAuthRepository.createOAuth(oauth);
          newTool.oauthId = savedOAuth.id;
        }
      }

      return await this.userToolsCustomRepository.createCustomTool(newTool);
    } catch (error) {
      this.logger.error(`Lỗi khi tạo công cụ tùy chỉnh với xác thực: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_CREATE_FAILED, 'Tạo công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Lấy danh sách công cụ tùy chỉnh của người dùng
   * @param userId ID của người dùng
   * @param query Tham số truy vấn
   * @returns Danh sách công cụ tùy chỉnh phân trang
   */
  async getCustomTools(userId: number, query: QueryDto): Promise<PaginatedResult<ToolResponseDto>> {
    try {
      // Tạo đối tượng tham số truy vấn
      const queryParams = {
        userId,
        page: query.page,
        limit: query.limit,
        search: query.search,
        sortBy: query.sortBy,
        sortDirection: query.sortDirection,
      };

      const result = await this.userToolsCustomRepository.findByUserId(queryParams);

      // Chuyển đổi từ entity sang DTO
      const toolDtos = result.items.map(tool => this.mapCustomToolToDto(tool));

      return {
        items: toolDtos,
        meta: result.meta
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, 'Không thể lấy danh sách công cụ tùy chỉnh');
    }
  }

  /**
   * Lấy chi tiết công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Chi tiết công cụ tùy chỉnh
   */
  async getCustomToolDetail(userId: number, toolId: string): Promise<ToolDetailResponseDto> {
    try {
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${toolId}`);
      }

      return await this.mapCustomToolToDetailDto(tool);
    } catch (error) {
      this.logger.error(`Lỗi khi lấy chi tiết công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không thể lấy chi tiết công cụ tùy chỉnh với ID ${toolId}`);
    }
  }

  /**
   * Cập nhật xác thực cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async updateCustomToolAuth(userId: number, updateDto: UpdateToolAuthDto): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(updateDto.toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${updateDto.toolId}`);
      }

      // Xóa cấu hình xác thực cũ nếu có
      if (updateDto.oldAuthId) {
        if (tool.apiKeyId === updateDto.oldAuthId) {
          await this.apiKeyRepository.deleteApiKey(updateDto.oldAuthId);
          tool.apiKeyId = null;
        } else if (tool.oauthId === updateDto.oldAuthId) {
          await this.oAuthRepository.deleteOAuth(updateDto.oldAuthId);
          tool.oauthId = null;
        }
      }

      // Tạo cấu hình xác thực mới
      if (updateDto.authConfig.authType === AuthTypeEnum.API_KEY) {
        const apiKeyDto = updateDto.authConfig as ApiKeyAuthDto;
        const apiKey = new ApiKey();
        apiKey.schemeName = apiKeyDto.schemeName || 'apiKey';
        apiKey.apiKey = apiKeyDto.apiKey;
        apiKey.apiKeyLocation = apiKeyDto.apiKeyLocation;
        apiKey.paramName = apiKeyDto.paramName || 'X-API-KEY';

        const savedApiKey = await this.apiKeyRepository.createApiKey(apiKey);
        tool.apiKeyId = savedApiKey.id;
        tool.oauthId = null;
      } else if (updateDto.authConfig.authType === AuthTypeEnum.OAUTH) {
        const oauthDto = updateDto.authConfig as OAuthAuthDto;
        const oauth = new OAuth();
        oauth.schemeName = oauthDto.schemeName || 'oauth2';
        oauth.token = oauthDto.token;
        oauth.tokenSource = oauthDto.tokenSource;
        oauth.clientId = oauthDto.clientId || null;
        oauth.clientSecret = oauthDto.clientSecret || null;
        oauth.authUrl = oauthDto.authUrl || null;
        oauth.tokenUrl = oauthDto.tokenUrl || null;
        oauth.refreshUrl = oauthDto.refreshUrl || null;
        oauth.refreshToken = oauthDto.refreshToken || null;
        oauth.scopes = oauthDto.scopes || null;
        oauth.flowType = oauthDto.flowType || null;

        const savedOAuth = await this.oAuthRepository.createOAuth(oauth);
        tool.oauthId = savedOAuth.id;
        tool.apiKeyId = null;
      } else {
        // Không xác thực
        tool.apiKeyId = null;
        tool.oauthId = null;
      }

      // Cập nhật công cụ
      await this.userToolsCustomRepository.updateCustomTool(tool.id, tool);

      return { message: 'Cập nhật xác thực công cụ thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật xác thực công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED, 'Cập nhật xác thực công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Xóa công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param toolId ID của công cụ
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteCustomTool(userId: number, toolId: string): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${toolId}`);
      }

      // Xóa cấu hình xác thực nếu có
      if (tool.apiKeyId) {
        await this.apiKeyRepository.deleteApiKey(tool.apiKeyId);
      }

      if (tool.oauthId) {
        await this.oAuthRepository.deleteOAuth(tool.oauthId);
      }

      // Xóa công cụ
      await this.userToolsCustomRepository.deleteCustomTool(toolId);

      return { message: 'Xóa công cụ tùy chỉnh thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi xóa công cụ tùy chỉnh: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_DELETE_FAILED, 'Xóa công cụ tùy chỉnh thất bại');
    }
  }

  /**
   * Chuyển đổi từ entity sang DTO cơ bản
   * @param tool Entity công cụ tùy chỉnh
   * @returns DTO công cụ tùy chỉnh
   */
  private mapCustomToolToDto(tool: UserToolsCustom): ToolResponseDto {
    const dto = new ToolResponseDto();
    dto.id = tool.id;
    dto.toolName = tool.toolName;
    dto.toolDescription = tool.toolDescription || '';
    dto.endpoint = tool.endpoint;
    dto.method = tool.method;
    dto.status = tool.status;
    dto.createdAt = tool.createdAt;
    dto.updatedAt = tool.updatedAt;

    // Xác định loại xác thực
    if (tool.apiKeyId) {
      dto.authType = AuthTypeEnum.API_KEY;
    } else if (tool.oauthId) {
      dto.authType = AuthTypeEnum.OAUTH;
    } else {
      dto.authType = AuthTypeEnum.NONE;
    }

    return dto;
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param tool Entity công cụ tùy chỉnh
   * @returns DTO chi tiết công cụ tùy chỉnh
   */
  private async mapCustomToolToDetailDto(tool: UserToolsCustom): Promise<ToolDetailResponseDto> {
    const dto = this.mapCustomToolToDto(tool) as ToolDetailResponseDto;

    // Tạo cấu trúc parameters mới theo yêu cầu
    dto.parameters = {
      name: tool.toolName,
      description: tool.toolDescription || '',
      inputSchema: {
        type: "object",
        properties: {
          query_param: {
            type: "object",
            description: "Các tham số truy vấn",
            properties: tool.queryParam || {}
          },
          path_param: {
            type: "object",
            description: "Các tham số đường dẫn",
            properties: tool.pathParam || {}
          },
          body: {
            type: "object",
            description: "Các tham số body",
            properties: tool.body || {}
          }
        }
      }
    };

    dto.baseUrl = tool.baseUrl; // Thêm baseUrl vào DTO

    // Thêm thông tin extra
    dto.extra = {
      url: `${tool.baseUrl}${tool.endpoint}`,
      method: tool.method,
      headers: {}
    };

    // Thêm thông tin chi tiết về xác thực
    if (tool.apiKeyId) {
      // Lấy thông tin API Key từ repository
      const apiKey = await this.apiKeyRepository.findById(tool.apiKeyId);
      if (apiKey) {
        dto.apiKeyAuth = {
          id: apiKey.id,
          schemeName: apiKey.schemeName,
          apiKeyLocation: apiKey.apiKeyLocation
        };

        // Thêm header xác thực vào extra nếu là API Key
        if (apiKey.apiKeyLocation === 'header') {
          // Sử dụng giá trị paramName từ entity
          const headerName = apiKey.paramName || 'X-API-KEY';
          // Sử dụng giá trị thực tế của API key thay vì placeholder
          dto.extra.headers[headerName] = apiKey.apiKey || '';
        }
      }
    }

    if (tool.oauthId) {
      // Lấy thông tin OAuth từ repository
      const oauth = await this.oAuthRepository.findById(tool.oauthId);
      if (oauth) {
        dto.oauthAuth = {
          id: oauth.id,
          schemeName: oauth.schemeName,
          tokenSource: oauth.tokenSource,
          tokenExpiresAt: oauth.tokenExpiresAt || 0
        };

        // Thêm header xác thực vào extra nếu là OAuth
        // Sử dụng giá trị thực tế của token thay vì placeholder
        dto.extra.headers['Authorization'] = oauth.token ? `Bearer ${oauth.token}` : 'Bearer ';
      }
    }

    return dto;
  }

  /**
   * Cập nhật base URL cho công cụ tùy chỉnh
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật base URL
   * @returns Thông báo kết quả
   */
  @Transactional()
  async updateBaseUrl(userId: number, updateDto: UpdateBaseUrlDto): Promise<{ message: string }> {
    try {
      // Kiểm tra xem công cụ có tồn tại không
      const tool = await this.userToolsCustomRepository.findById(updateDto.toolId);

      if (!tool || tool.userId !== userId) {
        throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_NOT_FOUND, `Không tìm thấy công cụ tùy chỉnh với ID ${updateDto.toolId}`);
      }

      // Cập nhật base URL
      await this.userToolsCustomRepository.updateCustomTool(updateDto.toolId, {
        baseUrl: updateDto.baseUrl
      });

      return { message: 'Cập nhật base URL thành công' };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật base URL: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(CUSTOM_TOOLS_ERROR_CODES.CUSTOM_TOOL_UPDATE_FAILED, 'Cập nhật base URL thất bại');
    }
  }
}
