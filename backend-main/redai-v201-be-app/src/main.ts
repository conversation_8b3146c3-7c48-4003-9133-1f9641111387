import { NestFactory } from '@nestjs/core';
import { AppModule } from '@app';
import { SwaggerModule } from '@nestjs/swagger';
import { VersioningType } from '@nestjs/common';
import { createSwaggerConfig, swaggerCustomOptions } from '@common/swagger';
import { ConfigService } from '@nestjs/config';
import * as dotenv from 'dotenv';
import { initializeTypeOrmTransactional } from '@config/typeorm-transactional.config';
import { corsConfig } from '@common/filters/cors.config';
import * as bodyParser from 'body-parser';
import { CustomValidationPipe } from '@common/pipes/custom-validation.pipe';

// Khởi tạo typeorm-transactional
initializeTypeOrmTransactional();

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // C<PERSON>u hình giới hạn kích thước request body
  app.use(bodyParser.json({ limit: '50mb' }));
  app.use(bodyParser.urlencoded({ limit: '50mb', extended: true }));

  // Thêm global prefix với version
  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  })

  // Cấu hình custom validation pipe
  app.useGlobalPipes(new CustomValidationPipe());

  // Cấu hình CORS với danh sách các frontend được phép
  app.enableCors(corsConfig);

  // Cấu hình Swagger với thông tin từ biến môi trường
  const configService = app.get(ConfigService);
  const swaggerConfig = createSwaggerConfig(configService);
  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup('api/docs', app, document, swaggerCustomOptions);

  // Khởi động server
  const port = process.env.PORT ?? 3000;
  await app.listen(port);
  console.log(`Application is running on: http://localhost:${port}`);
  console.log(`Swagger documentation is available at: http://localhost:${port}/api/docs`);
}
bootstrap().catch((err) => {
  console.error(err);
  process.exit(1);
});
