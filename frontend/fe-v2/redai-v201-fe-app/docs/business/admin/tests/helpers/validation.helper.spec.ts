import { Test, TestingModule } from '@nestjs/testing';
import { ValidationHelper } from '../../helpers/validation.helper';
import { AppException } from '@common/exceptions';
import { BUSINESS_ADMIN_ERROR_CODES } from '../../exceptions/business-admin.exception';
import { PriceTypeEnum } from '@modules/business/enums/price-type.enum';
import { EntityStatusEnum } from '@modules/business/enums/entity-status.enum';
import { UserProduct } from '@modules/business/entities';

describe('ValidationHelper', () => {
  let validationHelper: ValidationHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ValidationHelper],
    }).compile();

    validationHelper = module.get<ValidationHelper>(ValidationHelper);
  });

  describe('validateProductPriceStructure', () => {
    // HAS_PRICE tests
    it('nên x<PERSON>c thực gi<PERSON> hợp lệ với loại giá HAS_PRICE', () => {
      // Arrange
      const price = {
        currency: 'VND',
        listPrice: 1000000,
        salePrice: 900000,
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).not.toThrow();
    });

    it('nên ném lỗi khi giá không phải là object với loại giá HAS_PRICE', () => {
      // Arrange
      const price = 'not an object';

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Giá sản phẩm phải là một đối tượng khi loại giá là HAS_PRICE');
      }
    });

    it('nên ném lỗi khi thiếu currency với loại giá HAS_PRICE', () => {
      // Arrange
      const price = {
        listPrice: 1000000,
        salePrice: 900000,
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Giá sản phẩm phải có đủ currency, listPrice và salePrice khi loại giá là HAS_PRICE');
      }
    });

    it('nên ném lỗi khi thiếu listPrice với loại giá HAS_PRICE', () => {
      // Arrange
      const price = {
        currency: 'VND',
        salePrice: 900000,
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).toThrow(AppException);
    });

    it('nên ném lỗi khi thiếu salePrice với loại giá HAS_PRICE', () => {
      // Arrange
      const price = {
        currency: 'VND',
        listPrice: 1000000,
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).toThrow(AppException);
    });

    it('nên ném lỗi khi salePrice lớn hơn listPrice với loại giá HAS_PRICE', () => {
      // Arrange
      const price = {
        currency: 'VND',
        listPrice: 1000000,
        salePrice: 1100000,
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, PriceTypeEnum.HAS_PRICE);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Giá bán (salePrice) phải nhỏ hơn hoặc bằng giá niêm yết (listPrice)');
      }
    });

    // STRING_PRICE tests
    it('nên xác thực giá hợp lệ với loại giá STRING_PRICE', () => {
      // Arrange
      const price = {
        priceDescription: 'Giá liên hệ',
      };

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.STRING_PRICE)).not.toThrow();
    });

    it('nên ném lỗi khi giá không phải là object với loại giá STRING_PRICE', () => {
      // Arrange
      const price = 'not an object';

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.STRING_PRICE)).toThrow(AppException);
    });

    it('nên ném lỗi khi thiếu priceDescription với loại giá STRING_PRICE', () => {
      // Arrange
      const price = {};

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.STRING_PRICE)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, PriceTypeEnum.STRING_PRICE);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Giá sản phẩm phải có trường priceDescription khi loại giá là STRING_PRICE');
      }
    });

    // NO_PRICE tests
    it('nên xác thực giá hợp lệ với loại giá NO_PRICE', () => {
      // Arrange
      const price = null;

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.NO_PRICE)).not.toThrow();
    });

    it('nên ném lỗi khi giá không phải là null với loại giá NO_PRICE', () => {
      // Arrange
      const price = {};

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, PriceTypeEnum.NO_PRICE)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, PriceTypeEnum.NO_PRICE);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Giá sản phẩm phải là null khi loại giá là NO_PRICE');
      }
    });

    // Invalid typePrice test
    it('nên ném lỗi khi loại giá không hợp lệ', () => {
      // Arrange
      const price = {};
      const invalidTypePrice = 'INVALID_TYPE' as unknown as PriceTypeEnum;

      // Act & Assert
      expect(() => validationHelper.validateProductPriceStructure(price, invalidTypePrice)).toThrow(AppException);
      try {
        validationHelper.validateProductPriceStructure(price, invalidTypePrice);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.PRODUCT_PRICE_VALIDATION_ERROR);
        expect(error.message).toBe('Loại giá không hợp lệ');
      }
    });
  });

  describe('validateProductPrice', () => {
    it('nên xác thực sản phẩm với cấu trúc giá hợp lệ', () => {
      // Arrange
      const product = {
        id: 1,
        name: 'Test Product',
        price: {
          currency: 'VND',
          listPrice: 1000000,
          salePrice: 900000,
        },
        typePrice: PriceTypeEnum.HAS_PRICE,
      } as UserProduct;

      // Act & Assert
      expect(() => validationHelper.validateProductPrice(product)).not.toThrow();
    });

    it('nên ném lỗi khi sản phẩm có cấu trúc giá không hợp lệ', () => {
      // Arrange
      const product = {
        id: 1,
        name: 'Test Product',
        price: {
          currency: 'VND',
          listPrice: 1000000,
        },
        typePrice: PriceTypeEnum.HAS_PRICE,
      } as UserProduct;

      // Act & Assert
      expect(() => validationHelper.validateProductPrice(product)).toThrow(AppException);
    });

    it('nên ném lỗi khi sản phẩm là null', () => {
      // Arrange
      const product = null;

      // Act & Assert
      expect(() => validationHelper.validateProductPrice(product)).toThrow(AppException);
      try {
        validationHelper.validateProductPrice(product);
      } catch (error) {
        expect(error.errorCode).toBe(BUSINESS_ADMIN_ERROR_CODES.USER_PRODUCT_NOT_FOUND);
        expect(error.message).toBe('Sản phẩm không tồn tại');
      }
    });
  });
});
