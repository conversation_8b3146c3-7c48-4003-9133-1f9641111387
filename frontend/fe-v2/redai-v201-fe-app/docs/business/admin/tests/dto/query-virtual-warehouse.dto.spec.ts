import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { QueryVirtualWarehouseDto } from '../../dto/warehouse/query-virtual-warehouse.dto';

describe('QueryVirtualWarehouseDto', () => {
  it('nên chuyển đổi plain object thành instance của QueryVirtualWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      associatedSystem: 'ERP',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(QueryVirtualWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryVirtualWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.associatedSystem).toBe('ERP');
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của QueryVirtualWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(QueryVirtualWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryVirtualWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.associatedSystem).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryVirtualWarehouseDto, {
      page: 1,
      limit: 10,
      associatedSystem: 'ERP'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với associatedSystem không phải là string', async () => {
    // Arrange
    const dto = plainToInstance(QueryVirtualWarehouseDto, {
      page: 1,
      limit: 10,
      associatedSystem: 123
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('associatedSystem');
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
