import { plainToInstance } from 'class-transformer';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('WarehouseResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của WarehouseResponseDto', () => {
    // Arrange
    const plainObject = {
      warehouseId: 1,
      name: '<PERSON><PERSON> ch<PERSON>h',
      description: '<PERSON>ho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(WarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('<PERSON><PERSON> ch<PERSON>');
    expect(dto.description).toBe('<PERSON><PERSON> chứa hàng hóa chính của công ty');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của WarehouseResponseDto', () => {
    // Arrange
    const plainObject = {
      warehouseId: 2,
      name: 'Kho ảo',
      type: WarehouseTypeEnum.VIRTUAL
    };

    // Act
    const dto = plainToInstance(WarehouseResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.name).toBe('Kho ảo');
    expect(dto.description).toBeUndefined();
    expect(dto.type).toBe(WarehouseTypeEnum.VIRTUAL);
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const data = {
      warehouseId: 3,
      name: 'Kho phụ',
      description: 'Kho phụ trợ',
      type: WarehouseTypeEnum.PHYSICAL
    };

    // Act
    const dto = new WarehouseResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseResponseDto);
    expect(dto.warehouseId).toBe(3);
    expect(dto.name).toBe('Kho phụ');
    expect(dto.description).toBe('Kho phụ trợ');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
  });
});
