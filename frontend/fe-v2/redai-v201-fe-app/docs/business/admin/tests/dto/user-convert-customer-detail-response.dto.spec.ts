import { plainToInstance } from 'class-transformer';
import { UserConvertCustomerDetailResponseDto } from '../../dto/userconverts/user-convert-customer-detail-response.dto';
import { UserConvertResponseDto } from '../../dto/userconverts/user-convert-response.dto';

describe('UserConvertCustomerDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserConvertCustomerDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      avatar: 'avatars/customer-123.jpg',
      name: '<PERSON><PERSON><PERSON><PERSON>n <PERSON>',
      email: { primary: '<EMAIL>', secondary: '<EMAIL>' },
      phone: '0912345678',
      platform: 'Facebook',
      timezone: 'Asia/Ho_Chi_Minh',
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userId: 123,
      agentId: '550e8400-e29b-41d4-a716-446655440000',
      metadata: [
        { fieldName: 'address', fieldValue: 'Hà Nội' },
        { fieldName: 'job', fieldValue: 'Developer' }
      ],
      converts: [
        {
          id: 1,
          convertCustomerId: 1,
          userId: 123,
          conversionType: 'online',
          source: 'website',
          notes: 'Khách hàng quan tâm đến sản phẩm X',
          content: { additionalInfo: 'Thông tin thêm về khách hàng' },
          createdAt: 1625097600000,
          updatedAt: 1625184000000
        },
        {
          id: 2,
          convertCustomerId: 1,
          userId: 123,
          conversionType: 'offline',
          source: 'event',
          notes: 'Khách hàng tham gia sự kiện Y',
          content: { additionalInfo: 'Thông tin thêm về khách hàng từ sự kiện' },
          createdAt: 1625184000000,
          updatedAt: 1625184000000
        }
      ]
    };

    // Act
    const dto = plainToInstance(UserConvertCustomerDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertCustomerDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.avatar).toBe('avatars/customer-123.jpg');
    expect(dto.name).toBe('Nguyễn Văn A');
    expect(dto.email).toEqual({ primary: '<EMAIL>', secondary: '<EMAIL>' });
    expect(dto.phone).toBe('0912345678');
    expect(dto.platform).toBe('Facebook');
    expect(dto.timezone).toBe('Asia/Ho_Chi_Minh');
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
    expect(dto.userId).toBe(123);
    expect(dto.agentId).toBe('550e8400-e29b-41d4-a716-446655440000');
    expect(dto.metadata).toHaveLength(2);
    
    // Kiểm tra danh sách bản ghi chuyển đổi
    expect(dto.converts).toBeDefined();
    expect(dto.converts).toHaveLength(2);
    expect(dto.converts[0]).toBeInstanceOf(UserConvertResponseDto);
    expect(dto.converts[0].id).toBe(1);
    expect(dto.converts[0].conversionType).toBe('online');
    expect(dto.converts[1]).toBeInstanceOf(UserConvertResponseDto);
    expect(dto.converts[1].id).toBe(2);
    expect(dto.converts[1].conversionType).toBe('offline');
  });

  it('nên xử lý đúng khi converts là mảng rỗng', () => {
    // Arrange
    const plainObject = {
      id: 1,
      avatar: 'avatars/customer-123.jpg',
      name: 'Nguyễn Văn A',
      email: { primary: '<EMAIL>' },
      phone: '0912345678',
      platform: 'Facebook',
      timezone: 'Asia/Ho_Chi_Minh',
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      userId: 123,
      agentId: '550e8400-e29b-41d4-a716-446655440000',
      metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }],
      converts: []
    };

    // Act
    const dto = plainToInstance(UserConvertCustomerDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertCustomerDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.converts).toHaveLength(0);
  });
});
