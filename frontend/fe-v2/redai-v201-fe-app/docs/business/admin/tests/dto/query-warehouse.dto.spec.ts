import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { QueryWarehouseDto } from '../../dto/warehouse/query-warehouse.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

describe('QueryWarehouseDto', () => {
  it('nên chuyển đổi plain object thành instance của QueryWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      type: WarehouseTypeEnum.PHYSICAL,
      search: '<PERSON>ho chính',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(QueryWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.search).toBe('<PERSON><PERSON> chính');
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của QueryWarehouseDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(QueryWarehouseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryWarehouseDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.type).toBeUndefined();
    expect(dto.search).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 1,
      limit: 10,
      type: WarehouseTypeEnum.PHYSICAL,
      search: 'Kho chính'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với type không hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 1,
      limit: 10,
      type: 'INVALID_TYPE',
      search: 'Kho chính'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('type');
    expect(errors[0].constraints).toHaveProperty('isEnum');
  });

  it('nên validate thất bại với search không phải là string', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseDto, {
      page: 1,
      limit: 10,
      type: WarehouseTypeEnum.PHYSICAL,
      search: 123
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('search');
    expect(errors[0].constraints).toHaveProperty('isString');
  });
});
