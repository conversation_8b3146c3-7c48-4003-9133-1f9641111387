import { plainToInstance } from 'class-transformer';
import { UserConvertDetailResponseDto } from '../../dto/userconverts/user-convert-detail-response.dto';
import { UserConvertCustomerResponseDto } from '../../dto/userconverts/user-convert-customer-response.dto';

describe('UserConvertDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserConvertDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      convertCustomerId: 2,
      userId: 3,
      conversionType: 'online',
      source: 'website',
      notes: 'Khách hàng quan tâm đến sản phẩm X',
      content: { additionalInfo: 'Thông tin thêm về khách hàng' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      customer: {
        id: 2,
        avatar: 'avatars/customer-123.jpg',
        name: '<PERSON><PERSON><PERSON><PERSON>',
        email: { primary: '<EMAIL>' },
        phone: '0912345678',
        platform: 'Facebook',
        timezone: 'Asia/Ho_Chi_Minh',
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        userId: 3,
        agentId: '550e8400-e29b-41d4-a716-446655440000',
        metadata: []
      }
    };

    // Act
    const dto = plainToInstance(UserConvertDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.convertCustomerId).toBe(2);
    expect(dto.userId).toBe(3);
    expect(dto.conversionType).toBe('online');
    expect(dto.source).toBe('website');
    expect(dto.notes).toBe('Khách hàng quan tâm đến sản phẩm X');
    expect(dto.content).toEqual({ additionalInfo: 'Thông tin thêm về khách hàng' });
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
    
    // Kiểm tra thông tin khách hàng
    expect(dto.customer).toBeDefined();
    expect(dto.customer).toBeInstanceOf(UserConvertCustomerResponseDto);
    expect(dto.customer?.id).toBe(2);
    expect(dto.customer?.name).toBe('Nguyễn Văn A');
    expect(dto.customer?.email).toEqual({ primary: '<EMAIL>' });
    expect(dto.customer?.phone).toBe('0912345678');
    expect(dto.customer?.platform).toBe('Facebook');
    expect(dto.customer?.userId).toBe(3);
  });

  it('nên xử lý đúng khi customer là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      convertCustomerId: 2,
      userId: 3,
      conversionType: 'online',
      source: 'website',
      notes: 'Khách hàng quan tâm đến sản phẩm X',
      content: { additionalInfo: 'Thông tin thêm về khách hàng' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      customer: null
    };

    // Act
    const dto = plainToInstance(UserConvertDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.customer).toBeNull();
  });

  it('nên xử lý đúng khi không có trường customer', () => {
    // Arrange
    const plainObject = {
      id: 1,
      convertCustomerId: 2,
      userId: 3,
      conversionType: 'online',
      source: 'website',
      notes: 'Khách hàng quan tâm đến sản phẩm X',
      content: { additionalInfo: 'Thông tin thêm về khách hàng' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000
    };

    // Act
    const dto = plainToInstance(UserConvertDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertDetailResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.customer).toBeUndefined();
  });
});
