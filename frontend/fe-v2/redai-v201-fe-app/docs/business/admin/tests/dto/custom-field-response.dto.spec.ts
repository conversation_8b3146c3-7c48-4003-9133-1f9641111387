import { plainToInstance } from 'class-transformer';
import { CustomFieldResponseDto } from '../../dto/customfields/custom-field-response.dto';

describe('CustomFieldResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của CustomFieldResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
        description: '<PERSON><PERSON><PERSON> sắc chính của sản phẩm',
      },
      employeeId: 1,
      createAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(CustomFieldResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.component).toBe('input');
    expect(dto.configId).toBe('product_color');
    expect(dto.label).toBe('Màu sắc');
    expect(dto.type).toBe('text');
    expect(dto.required).toBe(true);
    expect(dto.configJson).toEqual({
      placeholder: 'Nhập màu sắc',
      maxLength: 50,
      description: 'Màu sắc chính của sản phẩm',
    });
    expect(dto.employeeId).toBe(1);
    expect(dto.createAt).toBe(1625097600000);
  });

  it('nên xử lý đúng khi employeeId là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      component: 'input',
      configId: 'product_color',
      label: 'Màu sắc',
      type: 'text',
      required: true,
      configJson: {
        placeholder: 'Nhập màu sắc',
        maxLength: 50,
      },
      employeeId: null,
      createAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(CustomFieldResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldResponseDto);
    expect(dto.employeeId).toBeNull();
  });

  it('nên xử lý đúng với configJson phức tạp', () => {
    // Arrange
    const plainObject = {
      id: 1,
      component: 'select',
      configId: 'product_category',
      label: 'Danh mục',
      type: 'select',
      required: true,
      configJson: {
        placeholder: 'Chọn danh mục',
        options: [
          { label: 'Điện thoại', value: 'phone' },
          { label: 'Máy tính', value: 'computer' },
          { label: 'Phụ kiện', value: 'accessory' },
        ],
        defaultValue: 'phone',
        description: 'Danh mục sản phẩm',
      },
      employeeId: 1,
      createAt: 1625097600000,
    };

    // Act
    const dto = plainToInstance(CustomFieldResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldResponseDto);
    expect(dto.configJson.options).toBeDefined();
    expect(dto.configJson.options).toHaveLength(3);
    expect(dto.configJson.options?.[0].label).toBe('Điện thoại');
    expect(dto.configJson.options?.[0].value).toBe('phone');
    expect(dto.configJson.defaultValue).toBe('phone');
  });

  it('nên xử lý đúng với mảng các CustomFieldResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        component: 'input',
        configId: 'product_color',
        label: 'Màu sắc',
        type: 'text',
        required: true,
        configJson: {
          placeholder: 'Nhập màu sắc',
          maxLength: 50,
        },
        employeeId: 1,
        createAt: 1625097600000,
      },
      {
        id: 2,
        component: 'textarea',
        configId: 'product_description',
        label: 'Mô tả',
        type: 'text',
        required: false,
        configJson: {
          placeholder: 'Nhập mô tả',
          maxLength: 500,
        },
        employeeId: 1,
        createAt: 1625097600000,
      },
    ];

    // Act
    const dtos = plainToInstance(CustomFieldResponseDto, plainArray);

    // Assert
    expect(dtos).toHaveLength(2);
    expect(dtos[0]).toBeInstanceOf(CustomFieldResponseDto);
    expect(dtos[1]).toBeInstanceOf(CustomFieldResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[1].id).toBe(2);
    expect(dtos[0].component).toBe('input');
    expect(dtos[1].component).toBe('textarea');
  });
});
