import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { FileQueryDto } from '../../dto/file/file-query.dto';

describe('FileQueryDto', () => {
  it('nên chuyển đổi plain object thành instance của FileQueryDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      folderId: 5,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FileQueryDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileQueryDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.folderId).toBe(5);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của FileQueryDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(FileQueryDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FileQueryDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.folderId).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(FileQueryDto, {
      page: 1,
      limit: 10,
      folderId: 5
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với folderId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(FileQueryDto, {
      page: 1,
      limit: 10,
      folderId: 'not-a-number'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('folderId');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });
});
