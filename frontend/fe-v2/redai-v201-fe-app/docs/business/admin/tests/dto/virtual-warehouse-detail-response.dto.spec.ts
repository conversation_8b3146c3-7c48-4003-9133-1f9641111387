import { plainToInstance } from 'class-transformer';
import { VirtualWarehouseDetailResponseDto } from '../../dto/warehouse/virtual-warehouse-detail-response.dto';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { CustomFieldValueResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('VirtualWarehouseDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của VirtualWarehouseDetailResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 2,
      name: '<PERSON>ho ảo',
      description: '<PERSON>ho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL
    };

    const plainObject = {
      warehouseId: 2,
      associatedSystem: 'ERP System',
      purpose: 'Digital inventory management',
      warehouse: warehouseInfo,
      customFields: [
        {
          fieldId: 5,
          label: 'API Version',
          value: {
            value: 'v2.1'
          }
        },
        {
          fieldId: 6,
          label: 'Yêu cầu xác thực',
          value: {
            value: true
          }
        }
      ],
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.associatedSystem).toBe('ERP System');
    expect(dto.purpose).toBe('Digital inventory management');
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(2);
    expect(dto.warehouse.name).toBe('Kho ảo');
    expect(dto.warehouse.description).toBe('Kho ảo quản lý hàng hóa trực tuyến');
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.VIRTUAL);
    expect(dto.customFields).toBeDefined();
    expect(dto.customFields?.length).toBe(2);
    expect(dto.customFields?.[0]?.fieldId).toBe(5);
    expect(dto.customFields?.[0]?.label).toBe('API Version');
    expect(dto.customFields?.[0]?.value?.value).toBe('v2.1');
    expect(dto.customFields?.[1]?.fieldId).toBe(6);
    expect(dto.customFields?.[1]?.label).toBe('Yêu cầu xác thực');
    expect(dto.customFields?.[1]?.value?.value).toBe(true);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của VirtualWarehouseDetailResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 4,
      name: 'Kho ảo Cloud',
      type: WarehouseTypeEnum.VIRTUAL
    };

    const plainObject = {
      warehouseId: 4,
      associatedSystem: 'ERP Cloud',
      warehouse: warehouseInfo
    };

    // Act
    const dto = plainToInstance(VirtualWarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(4);
    expect(dto.associatedSystem).toBe('ERP Cloud');
    expect(dto.purpose).toBeUndefined();
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(4);
    expect(dto.warehouse.name).toBe('Kho ảo Cloud');
    expect(dto.warehouse.description).toBeUndefined();
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.VIRTUAL);
    expect(dto.customFields).toBeUndefined();
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const warehouseInfo = new WarehouseResponseDto({
      warehouseId: 2,
      name: 'Kho ảo',
      description: 'Kho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL
    });

    const customField = new CustomFieldValueResponseDto({
      fieldId: 5,
      label: 'API Version',
      value: {
        value: 'v2.1'
      }
    });

    const data = {
      warehouseId: 2,
      associatedSystem: 'ERP System',
      purpose: 'Digital inventory management',
      warehouse: warehouseInfo,
      customFields: [customField]
    };

    // Act
    const dto = new VirtualWarehouseDetailResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(VirtualWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.associatedSystem).toBe('ERP System');
    expect(dto.purpose).toBe('Digital inventory management');
    expect(dto.warehouse).toBe(warehouseInfo);
    expect(dto.customFields).toEqual([customField]);
  });
});
