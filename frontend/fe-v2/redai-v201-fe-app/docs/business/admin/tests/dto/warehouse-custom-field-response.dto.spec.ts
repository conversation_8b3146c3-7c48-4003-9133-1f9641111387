import { plainToInstance } from 'class-transformer';
import {
  WarehouseCustomFieldResponseDto,
  WarehouseCustomFieldDetailResponseDto,
  CustomFieldValueDto,
  CustomFieldConfigDto,
  FieldDetailsDto
} from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('WarehouseCustomFieldResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của WarehouseCustomFieldResponseDto', () => {
    // Arrange
    const plainObject = {
      warehouseId: 1,
      fieldId: 3,
      value: {
        value: 'North'
      },
      warehouseName: 'Kho chính',
      fieldLabel: 'Khu vực',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(WarehouseCustomFieldResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(3);
    expect(dto.value).toBeDefined();
    expect(dto.value.value).toBe('North');
    expect(dto.warehouseName).toBe('Kho chính');
    expect(dto.fieldLabel).toBe('Khu vực');
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const valueDto = new CustomFieldValueDto({
      value: 'North'
    });

    const data = {
      warehouseId: 1,
      fieldId: 3,
      value: valueDto,
      warehouseName: 'Kho chính',
      fieldLabel: 'Khu vực'
    };

    // Act
    const dto = new WarehouseCustomFieldResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(3);
    expect(dto.value).toBe(valueDto);
    expect(dto.warehouseName).toBe('Kho chính');
    expect(dto.fieldLabel).toBe('Khu vực');
  });
});

describe('WarehouseCustomFieldDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của WarehouseCustomFieldDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      warehouseId: 1,
      fieldId: 3,
      value: {
        value: 'North'
      },
      warehouseName: 'Kho chính',
      fieldLabel: 'Khu vực',
      fieldDetails: {
        label: 'Khu vực',
        type: 'TEXT',
        required: true,
        configJson: {
          options: ['North', 'South', 'East', 'West']
        }
      },
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(WarehouseCustomFieldDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(3);
    expect(dto.value).toBeDefined();
    expect(dto.value.value).toBe('North');
    expect(dto.warehouseName).toBe('Kho chính');
    expect(dto.fieldLabel).toBe('Khu vực');
    expect(dto.fieldDetails).toBeDefined();
    expect(dto.fieldDetails.label).toBe('Khu vực');
    expect(dto.fieldDetails.type).toBe('TEXT');
    expect(dto.fieldDetails.required).toBe(true);
    expect(dto.fieldDetails.configJson).toBeDefined();
    expect(dto.fieldDetails.configJson.options).toEqual(['North', 'South', 'East', 'West']);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const valueDto = new CustomFieldValueDto({
      value: 'North'
    });

    const configDto = new CustomFieldConfigDto({
      options: ['North', 'South', 'East', 'West']
    });

    const fieldDetailsDto = new FieldDetailsDto({
      label: 'Khu vực',
      type: 'TEXT',
      required: true,
      configJson: configDto
    });

    const data = {
      warehouseId: 1,
      fieldId: 3,
      value: valueDto,
      warehouseName: 'Kho chính',
      fieldLabel: 'Khu vực',
      fieldDetails: fieldDetailsDto
    };

    // Act
    const dto = new WarehouseCustomFieldDetailResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseCustomFieldDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(3);
    expect(dto.value).toBe(valueDto);
    expect(dto.warehouseName).toBe('Kho chính');
    expect(dto.fieldLabel).toBe('Khu vực');
    expect(dto.fieldDetails).toBe(fieldDetailsDto);
  });
});

describe('CustomFieldValueDto', () => {
  it('nên chuyển đổi plain object thành instance của CustomFieldValueDto', () => {
    // Arrange
    const plainObject = {
      value: 'North',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(CustomFieldValueDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldValueDto);
    expect(dto.value).toBe('North');
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên khởi tạo đúng từ constructor với các kiểu giá trị khác nhau', () => {
    // Arrange & Act
    const stringDto = new CustomFieldValueDto({ value: 'North' });
    const numberDto = new CustomFieldValueDto({ value: 123 });
    const booleanDto = new CustomFieldValueDto({ value: true });
    const arrayDto = new CustomFieldValueDto({ value: ['North', 'South'] });

    // Assert
    expect(stringDto.value).toBe('North');
    expect(numberDto.value).toBe(123);
    expect(booleanDto.value).toBe(true);
    expect(arrayDto.value).toEqual(['North', 'South']);
  });
});

describe('FieldDetailsDto', () => {
  it('nên chuyển đổi plain object thành instance của FieldDetailsDto', () => {
    // Arrange
    const plainObject = {
      label: 'Khu vực',
      type: 'TEXT',
      required: true,
      configJson: {
        options: ['North', 'South', 'East', 'West']
      },
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(FieldDetailsDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(FieldDetailsDto);
    expect(dto.label).toBe('Khu vực');
    expect(dto.type).toBe('TEXT');
    expect(dto.required).toBe(true);
    expect(dto.configJson).toBeDefined();
    expect(dto.configJson.options).toEqual(['North', 'South', 'East', 'West']);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const configDto = new CustomFieldConfigDto({
      options: ['North', 'South', 'East', 'West']
    });

    const data = {
      label: 'Khu vực',
      type: 'TEXT',
      required: true,
      configJson: configDto
    };

    // Act
    const dto = new FieldDetailsDto(data);

    // Assert
    expect(dto).toBeInstanceOf(FieldDetailsDto);
    expect(dto.label).toBe('Khu vực');
    expect(dto.type).toBe('TEXT');
    expect(dto.required).toBe(true);
    expect(dto.configJson).toBe(configDto);
  });
});

describe('CustomFieldConfigDto', () => {
  it('nên chuyển đổi plain object thành instance của CustomFieldConfigDto', () => {
    // Arrange
    const plainObject = {
      options: ['North', 'South', 'East', 'West'],
      min: 1,
      max: 10,
      pattern: '^[A-Z]',
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(CustomFieldConfigDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldConfigDto);
    expect(dto.options).toEqual(['North', 'South', 'East', 'West']);
    expect(dto.min).toBe(1);
    expect(dto.max).toBe(10);
    expect(dto.pattern).toBe('^[A-Z]');
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const data = {
      options: ['North', 'South', 'East', 'West'],
      min: 1,
      max: 10,
      pattern: '^[A-Z]'
    };

    // Act
    const dto = new CustomFieldConfigDto(data);

    // Assert
    expect(dto).toBeInstanceOf(CustomFieldConfigDto);
    expect(dto.options).toEqual(['North', 'South', 'East', 'West']);
    expect(dto.min).toBe(1);
    expect(dto.max).toBe(10);
    expect(dto.pattern).toBe('^[A-Z]');
  });
});
