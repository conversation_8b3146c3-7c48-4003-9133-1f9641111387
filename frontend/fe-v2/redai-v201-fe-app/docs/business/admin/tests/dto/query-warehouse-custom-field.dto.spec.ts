import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { QueryWarehouseCustomFieldDto } from '../../dto/warehouse/query-warehouse-custom-field.dto';

describe('QueryWarehouseCustomFieldDto', () => {
  it('nên chuyển đổi plain object thành instance của QueryWarehouseCustomFieldDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10,
      warehouseId: 1,
      fieldId: 3,
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(QueryWarehouseCustomFieldDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryWarehouseCustomFieldDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.warehouseId).toBe(1);
    expect(dto.fieldId).toBe(3);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của QueryWarehouseCustomFieldDto', () => {
    // Arrange
    const plainObject = {
      page: 1,
      limit: 10
    };

    // Act
    const dto = plainToInstance(QueryWarehouseCustomFieldDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(QueryWarehouseCustomFieldDto);
    expect(dto.page).toBe(1);
    expect(dto.limit).toBe(10);
    expect(dto.warehouseId).toBeUndefined();
    expect(dto.fieldId).toBeUndefined();
  });

  it('nên validate thành công với dữ liệu hợp lệ', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseCustomFieldDto, {
      page: 1,
      limit: 10,
      warehouseId: 1,
      fieldId: 3
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên validate thất bại với warehouseId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseCustomFieldDto, {
      page: 1,
      limit: 10,
      warehouseId: 'not-a-number',
      fieldId: 3
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('warehouseId');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });

  it('nên validate thất bại với fieldId không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(QueryWarehouseCustomFieldDto, {
      page: 1,
      limit: 10,
      warehouseId: 1,
      fieldId: 'not-a-number'
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].property).toBe('fieldId');
    expect(errors[0].constraints).toHaveProperty('isNumber');
  });
});
