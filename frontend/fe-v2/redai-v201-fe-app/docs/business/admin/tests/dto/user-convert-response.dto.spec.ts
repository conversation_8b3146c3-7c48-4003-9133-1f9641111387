import { plainToInstance } from 'class-transformer';
import { UserConvertResponseDto } from '../../dto/userconverts/user-convert-response.dto';

describe('UserConvertResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserConvertResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      convertCustomerId: 2,
      userId: 3,
      conversionType: 'online',
      source: 'website',
      notes: 'Khách hàng quan tâm đến sản phẩm X',
      content: { additionalInfo: 'Thông tin thêm về khách hàng' },
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserConvertResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.convertCustomerId).toBe(2);
    expect(dto.userId).toBe(3);
    expect(dto.conversionType).toBe('online');
    expect(dto.source).toBe('website');
    expect(dto.notes).toBe('Khách hàng quan tâm đến sản phẩm X');
    expect(dto.content).toEqual({ additionalInfo: 'Thông tin thêm về khách hàng' });
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
  });

  it('nên xử lý đúng khi các trường tùy chọn là null', () => {
    // Arrange
    const plainObject = {
      id: 1,
      convertCustomerId: null,
      userId: null,
      conversionType: null,
      source: null,
      notes: null,
      content: null,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
    };

    // Act
    const dto = plainToInstance(UserConvertResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserConvertResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.convertCustomerId).toBeNull();
    expect(dto.userId).toBeNull();
    expect(dto.conversionType).toBeNull();
    expect(dto.source).toBeNull();
    expect(dto.notes).toBeNull();
    expect(dto.content).toBeNull();
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);
  });

  it('nên xử lý đúng với mảng các UserConvertResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 1,
        convertCustomerId: 2,
        userId: 3,
        conversionType: 'online',
        source: 'website',
        notes: 'Khách hàng quan tâm đến sản phẩm X',
        content: { additionalInfo: 'Thông tin thêm về khách hàng' },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      },
      {
        id: 2,
        convertCustomerId: 2,
        userId: 3,
        conversionType: 'offline',
        source: 'event',
        notes: 'Khách hàng tham gia sự kiện Y',
        content: { additionalInfo: 'Thông tin thêm về khách hàng từ sự kiện' },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
      },
    ];

    // Act
    const dtos = plainToInstance(UserConvertResponseDto, plainArray);

    // Assert
    expect(Array.isArray(dtos)).toBe(true);
    expect(dtos.length).toBe(2);
    expect(dtos[0]).toBeInstanceOf(UserConvertResponseDto);
    expect(dtos[0].id).toBe(1);
    expect(dtos[0].conversionType).toBe('online');
    expect(dtos[1]).toBeInstanceOf(UserConvertResponseDto);
    expect(dtos[1].id).toBe(2);
    expect(dtos[1].conversionType).toBe('offline');
  });
});
