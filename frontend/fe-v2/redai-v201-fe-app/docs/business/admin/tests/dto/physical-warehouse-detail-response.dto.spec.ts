import { plainToInstance } from 'class-transformer';
import { PhysicalWarehouseDetailResponseDto } from '../../dto/warehouse/physical-warehouse-detail-response.dto';
import { WarehouseResponseDto } from '../../dto/warehouse/warehouse-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { CustomFieldValueResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('PhysicalWarehouseDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của PhysicalWarehouseDetailResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 1,
      name: '<PERSON>ho chính',
      description: '<PERSON>ho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL
    };

    const plainObject = {
      warehouseId: 1,
      address: '123 Storage St, Warehouse City',
      capacity: 5000,
      warehouse: warehouseInfo,
      customFields: [
        {
          fieldId: 3,
          label: '<PERSON>hu vực',
          value: {
            value: 'North'
          }
        },
        {
          fieldId: 4,
          label: '<PERSON>ó máy lạnh',
          value: {
            value: true
          }
        }
      ],
      extraField: 'Trường thừa không nên được chuyển đổi'
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.address).toBe('123 Storage St, Warehouse City');
    expect(dto.capacity).toBe(5000);
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(1);
    expect(dto.warehouse.name).toBe('Kho chính');
    expect(dto.warehouse.description).toBe('Kho chứa hàng hóa chính của công ty');
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.customFields).toBeDefined();
    expect(dto.customFields?.length).toBe(2);
    expect(dto.customFields?.[0]?.fieldId).toBe(3);
    expect(dto.customFields?.[0]?.label).toBe('Khu vực');
    expect(dto.customFields?.[0]?.value?.value).toBe('North');
    expect(dto.customFields?.[1]?.fieldId).toBe(4);
    expect(dto.customFields?.[1]?.label).toBe('Có máy lạnh');
    expect(dto.customFields?.[1]?.value?.value).toBe(true);
    // plainToInstance không loại bỏ các trường thừa mặc định, chỉ loại bỏ khi sử dụng excludeExtraneousValues
  });

  it('nên chuyển đổi plain object với các trường thiếu thành instance của PhysicalWarehouseDetailResponseDto', () => {
    // Arrange
    const warehouseInfo = {
      warehouseId: 3,
      name: 'Kho phụ',
      type: WarehouseTypeEnum.PHYSICAL
    };

    const plainObject = {
      warehouseId: 3,
      address: '45 Phố Huế, Hai Bà Trưng, Hà Nội',
      warehouse: warehouseInfo
    };

    // Act
    const dto = plainToInstance(PhysicalWarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(3);
    expect(dto.address).toBe('45 Phố Huế, Hai Bà Trưng, Hà Nội');
    expect(dto.capacity).toBeUndefined();
    expect(dto.warehouse).toBeDefined();
    expect(dto.warehouse.warehouseId).toBe(3);
    expect(dto.warehouse.name).toBe('Kho phụ');
    expect(dto.warehouse.description).toBeUndefined();
    expect(dto.warehouse.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.customFields).toBeUndefined();
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const warehouseInfo = new WarehouseResponseDto({
      warehouseId: 1,
      name: 'Kho chính',
      description: 'Kho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL
    });

    const customField = new CustomFieldValueResponseDto({
      fieldId: 3,
      label: 'Khu vực',
      value: {
        value: 'North'
      }
    });

    const data = {
      warehouseId: 1,
      address: '123 Storage St, Warehouse City',
      capacity: 5000,
      warehouse: warehouseInfo,
      customFields: [customField]
    };

    // Act
    const dto = new PhysicalWarehouseDetailResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(PhysicalWarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.address).toBe('123 Storage St, Warehouse City');
    expect(dto.capacity).toBe(5000);
    expect(dto.warehouse).toBe(warehouseInfo);
    expect(dto.customFields).toEqual([customField]);
  });
});
