import { plainToInstance } from 'class-transformer';
import { UserProductDetailResponseDto, CustomGroupFormResponseDto } from '../../dto/customfields/user-product-detail-response.dto';
import { PriceTypeEnum } from '../../../enums';

describe('UserProductDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của UserProductDetailResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: '<PERSON><PERSON> tả chi tiết về sản phẩm A',
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
        { url: 'https://cdn.redai.vn/products/image2.jpg', position: 1 },
      ],
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      customGroupForms: [{
        id: 1,
        label: 'Thông tin chi tiết',
        fields: [
          {
            id: 1,
            component: 'input',
            configId: 'product_color',
            label: 'Màu sắc',
            type: 'text',
            required: true,
            configJson: {
              placeholder: 'Nhập màu sắc',
              maxLength: 50,
            },
            employeeId: 1,
            createAt: 1625097600000,
          },
          {
            id: 2,
            component: 'textarea',
            configId: 'product_description',
            label: 'Mô tả',
            type: 'text',
            required: false,
            configJson: {
              placeholder: 'Nhập mô tả',
              maxLength: 500,
            },
            employeeId: 1,
            createAt: 1625097600000,
          },
        ],
      }],
    };

    // Act
    const dto = plainToInstance(UserProductDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductDetailResponseDto);
    expect(dto.id).toBe(123);
    expect(dto.name).toBe('Sản phẩm A');
    expect(dto.price).toEqual({ amount: 1000, currency: 'VND' });
    expect(dto.typePrice).toBe(PriceTypeEnum.HAS_PRICE);
    expect(dto.description).toBe('Mô tả chi tiết về sản phẩm A');
    expect(dto.images).toHaveLength(2);
    expect(dto.images).toBeDefined();
    if (dto.images) {
      expect(dto.images[0].url).toBe('https://cdn.redai.vn/products/image1.jpg');
      expect(dto.images[0].position).toBe(0);
    }
    expect(dto.tags).toEqual(['tag1', 'tag2']);
    expect(dto.createdBy).toBe(456);
    expect(dto.createdAt).toBe(1625097600000);
    expect(dto.updatedAt).toBe(1625184000000);

    expect(dto.customGroupForms).toBeDefined();
    if (dto.customGroupForms && dto.customGroupForms.length > 0) {
      expect(dto.customGroupForms[0].id).toBe(1);
      expect(dto.customGroupForms[0].label).toBe('Thông tin chi tiết');
      expect(dto.customGroupForms[0].fields).toHaveLength(2);
      expect(dto.customGroupForms[0].fields[0].id).toBe(1);
      expect(dto.customGroupForms[0].fields[0].component).toBe('input');
      expect(dto.customGroupForms[0].fields[1].id).toBe(2);
      expect(dto.customGroupForms[0].fields[1].component).toBe('textarea');
    }
  });

  it('nên xử lý đúng khi customGroupForm là null', () => {
    // Arrange
    const plainObject = {
      id: 123,
      name: 'Sản phẩm A',
      price: { amount: 1000, currency: 'VND' },
      typePrice: PriceTypeEnum.HAS_PRICE,
      description: 'Mô tả chi tiết về sản phẩm A',
      images: [
        { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
      ],
      tags: ['tag1', 'tag2'],
      createdBy: 456,
      createdAt: 1625097600000,
      updatedAt: 1625184000000,
      customGroupForms: [],
    };

    // Act
    const dto = plainToInstance(UserProductDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(UserProductDetailResponseDto);
    expect(dto.customGroupForms).toHaveLength(0);
  });

  it('nên xử lý đúng với mảng các UserProductDetailResponseDto', () => {
    // Arrange
    const plainArray = [
      {
        id: 123,
        name: 'Sản phẩm A',
        price: { amount: 1000, currency: 'VND' },
        typePrice: PriceTypeEnum.HAS_PRICE,
        description: 'Mô tả chi tiết về sản phẩm A',
        images: [
          { url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
        ],
        tags: ['tag1', 'tag2'],
        createdBy: 456,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        customGroupForms: [{
          id: 1,
          label: 'Thông tin chi tiết',
          fields: [
            {
              id: 1,
              component: 'input',
              configId: 'product_color',
              label: 'Màu sắc',
              type: 'text',
              required: true,
              configJson: {
                placeholder: 'Nhập màu sắc',
                maxLength: 50,
              },
              employeeId: 1,
              createAt: 1625097600000,
            },
          ],
        }],
      },
      {
        id: 124,
        name: 'Sản phẩm B',
        price: { amount: 2000, currency: 'VND' },
        typePrice: PriceTypeEnum.STRING_PRICE,
        description: 'Mô tả chi tiết về sản phẩm B',
        images: [
          { url: 'https://cdn.redai.vn/products/image3.jpg', position: 0 },
        ],
        tags: ['tag3', 'tag4'],
        createdBy: 456,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        customGroupForms: [],
      },
    ];

    // Act
    const dtos = plainToInstance(UserProductDetailResponseDto, plainArray);

    // Assert
    expect(dtos).toHaveLength(2);
    expect(dtos[0]).toBeInstanceOf(UserProductDetailResponseDto);
    expect(dtos[1]).toBeInstanceOf(UserProductDetailResponseDto);
    expect(dtos[0].id).toBe(123);
    expect(dtos[1].id).toBe(124);
    expect(dtos[0].customGroupForms).toBeDefined();
    if (dtos[0].customGroupForms && dtos[0].customGroupForms.length > 0) {
      expect(dtos[0].customGroupForms[0].id).toBe(1);
      expect(dtos[0].customGroupForms[0].fields).toHaveLength(1);
    }
    expect(dtos[1].customGroupForms).toHaveLength(0);
  });
});

describe('CustomGroupFormResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của CustomGroupFormResponseDto', () => {
    // Arrange
    const plainObject = {
      id: 1,
      label: 'Thông tin chi tiết',
      fields: [
        {
          id: 1,
          component: 'input',
          configId: 'product_color',
          label: 'Màu sắc',
          type: 'text',
          required: true,
          configJson: {
            placeholder: 'Nhập màu sắc',
            maxLength: 50,
          },
          employeeId: 1,
          createAt: 1625097600000,
        },
        {
          id: 2,
          component: 'textarea',
          configId: 'product_description',
          label: 'Mô tả',
          type: 'text',
          required: false,
          configJson: {
            placeholder: 'Nhập mô tả',
            maxLength: 500,
          },
          employeeId: 1,
          createAt: 1625097600000,
        },
      ],
    };

    // Act
    const dto = plainToInstance(CustomGroupFormResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomGroupFormResponseDto);
    expect(dto.id).toBe(1);
    expect(dto.label).toBe('Thông tin chi tiết');
    expect(dto.fields).toHaveLength(2);
    expect(dto.fields[0].id).toBe(1);
    expect(dto.fields[0].component).toBe('input');
    expect(dto.fields[1].id).toBe(2);
    expect(dto.fields[1].component).toBe('textarea');
  });

  it('nên xử lý đúng khi fields là mảng rỗng', () => {
    // Arrange
    const plainObject = {
      id: 1,
      label: 'Thông tin chi tiết',
      fields: [],
    };

    // Act
    const dto = plainToInstance(CustomGroupFormResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(CustomGroupFormResponseDto);
    expect(dto.fields).toHaveLength(0);
  });
});
