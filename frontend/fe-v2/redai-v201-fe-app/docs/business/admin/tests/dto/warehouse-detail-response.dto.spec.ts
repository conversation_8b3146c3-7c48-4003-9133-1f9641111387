import { plainToInstance } from 'class-transformer';
import { WarehouseDetailResponseDto, PhysicalWarehouseDetailsDto, VirtualWarehouseDetailsDto } from '../../dto/warehouse/warehouse-detail-response.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { CustomFieldValueResponseDto } from '../../dto/warehouse/warehouse-custom-field-response.dto';

describe('WarehouseDetailResponseDto', () => {
  it('nên chuyển đổi plain object thành instance của WarehouseDetailResponseDto với kho vật lý', () => {
    // Arrange
    const plainObject = {
      warehouseId: 1,
      name: '<PERSON><PERSON> chính',
      description: 'Kho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      details: {
        address: '123 Storage St, Warehouse City',
        capacity: 5000,
        area: 1000
      },
      customFields: [
        {
          fieldId: 3,
          label: '<PERSON><PERSON> vực',
          value: {
            value: 'North'
          }
        }
      ]
    };

    // Act
    const dto = plainToInstance(WarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('Kho chính');
    expect(dto.description).toBe('Kho chứa hàng hóa chính của công ty');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.details).toBeDefined();
    expect((dto.details as PhysicalWarehouseDetailsDto).address).toBe('123 Storage St, Warehouse City');
    expect((dto.details as PhysicalWarehouseDetailsDto).capacity).toBe(5000);
    expect((dto.details as PhysicalWarehouseDetailsDto).area).toBe(1000);
    expect(dto.customFields).toBeDefined();
    expect(dto.customFields?.length).toBe(1);
    expect(dto.customFields?.[0]?.fieldId).toBe(3);
    expect(dto.customFields?.[0]?.label).toBe('Khu vực');
    expect(dto.customFields?.[0]?.value?.value).toBe('North');
  });

  it('nên chuyển đổi plain object thành instance của WarehouseDetailResponseDto với kho ảo', () => {
    // Arrange
    const plainObject = {
      warehouseId: 2,
      name: 'Kho ảo',
      description: 'Kho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL,
      details: {
        associatedSystem: 'ERP System',
        purpose: 'Digital inventory management',
        url: 'https://virtual-warehouse.example.com/main',
        apiKey: 'vw_api_key_123'
      },
      customFields: [
        {
          fieldId: 5,
          label: 'API Version',
          value: {
            value: 'v2.1'
          }
        }
      ]
    };

    // Act
    const dto = plainToInstance(WarehouseDetailResponseDto, plainObject);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(2);
    expect(dto.name).toBe('Kho ảo');
    expect(dto.description).toBe('Kho ảo quản lý hàng hóa trực tuyến');
    expect(dto.type).toBe(WarehouseTypeEnum.VIRTUAL);
    expect(dto.details).toBeDefined();
    expect((dto.details as VirtualWarehouseDetailsDto).associatedSystem).toBe('ERP System');
    expect((dto.details as VirtualWarehouseDetailsDto).purpose).toBe('Digital inventory management');
    expect((dto.details as VirtualWarehouseDetailsDto).url).toBe('https://virtual-warehouse.example.com/main');
    expect((dto.details as VirtualWarehouseDetailsDto).apiKey).toBe('vw_api_key_123');
    expect(dto.customFields).toBeDefined();
    expect(dto.customFields?.length).toBe(1);
    expect(dto.customFields?.[0]?.fieldId).toBe(5);
    expect(dto.customFields?.[0]?.label).toBe('API Version');
    expect(dto.customFields?.[0]?.value?.value).toBe('v2.1');
  });

  it('nên trả về đúng kiểu details dựa trên loại kho', () => {
    // Arrange
    const physicalWarehouse = new WarehouseDetailResponseDto({
      warehouseId: 1,
      name: 'Kho chính',
      description: 'Kho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL
    });

    const virtualWarehouse = new WarehouseDetailResponseDto({
      warehouseId: 2,
      name: 'Kho ảo',
      description: 'Kho ảo quản lý hàng hóa trực tuyến',
      type: WarehouseTypeEnum.VIRTUAL
    });

    // Act & Assert
    expect(physicalWarehouse.getDetailsType()).toBe(PhysicalWarehouseDetailsDto);
    expect(virtualWarehouse.getDetailsType()).toBe(VirtualWarehouseDetailsDto);
  });

  it('nên khởi tạo đúng từ constructor', () => {
    // Arrange
    const customField = new CustomFieldValueResponseDto({
      fieldId: 3,
      label: 'Khu vực',
      value: {
        value: 'North'
      }
    });

    const data = {
      warehouseId: 1,
      name: 'Kho chính',
      description: 'Kho chứa hàng hóa chính của công ty',
      type: WarehouseTypeEnum.PHYSICAL,
      details: {
        address: '123 Storage St, Warehouse City',
        capacity: 5000
      },
      customFields: [customField]
    };

    // Act
    const dto = new WarehouseDetailResponseDto(data);

    // Assert
    expect(dto).toBeInstanceOf(WarehouseDetailResponseDto);
    expect(dto.warehouseId).toBe(1);
    expect(dto.name).toBe('Kho chính');
    expect(dto.description).toBe('Kho chứa hàng hóa chính của công ty');
    expect(dto.type).toBe(WarehouseTypeEnum.PHYSICAL);
    expect(dto.details).toEqual(data.details);
    expect(dto.customFields).toEqual([customField]);
  });
});
