/**
 * Mock data cho File
 */
export const mockFile = {
  id: 1,
  name: 'test-file.pdf',
  folderId: 1,
  size: 1024000,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho danh sách File
 */
export const mockFiles = [
  mockFile,
  {
    id: 2,
    name: 'document.docx',
    folderId: 1,
    size: 512000,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  },
  {
    id: 3,
    name: 'image.jpg',
    folderId: 2,
    size: 2048000,
    createdAt: 1625097600000,
    updatedAt: 1625097600000,
  },
];

/**
 * Mock data cho FileResponseDto
 */
export const mockFileResponseDto = {
  id: 1,
  name: 'test-file.pdf',
  folderId: 1,
  size: 1024000,
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
};

/**
 * Mock data cho FileDetailResponseDto
 */
export const mockFileDetailResponseDto = {
  id: 1,
  name: 'test-file.pdf',
  folder: {
    id: 1,
    name: 'Documents',
    path: '/Documents',
  },
  size: 1024000,
  formattedSize: '1 MB',
  fileType: {
    extension: 'pdf',
    mimeType: 'application/pdf',
    icon: 'document-icon',
  },
  previewUrl: 'https://cdn.redai.vn/preview/file-1',
  downloadUrl: 'https://cdn.redai.vn/download/file-1',
  createdAt: 1625097600000,
  formattedCreatedAt: '01/07/2021 00:00:00',
  updatedAt: 1625097600000,
  formattedUpdatedAt: '01/07/2021 00:00:00',
};

/**
 * Mock data cho PaginatedResult<FileResponseDto>
 */
export const mockFilesPaginatedResult = {
  items: [
    mockFileResponseDto,
    {
      id: 2,
      name: 'document.docx',
      folderId: 1,
      size: 512000,
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
    },
  ],
  meta: {
    currentPage: 1,
    itemsPerPage: 10,
    itemCount: 2,
    totalItems: 2,
    totalPages: 1,
  },
};
