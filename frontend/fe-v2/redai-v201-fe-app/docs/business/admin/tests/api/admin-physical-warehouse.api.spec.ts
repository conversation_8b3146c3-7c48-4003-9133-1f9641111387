import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { WarehouseTypeEnum } from '../../../enums';
import { AdminPhysicalWarehouseService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { PhysicalWarehouseResponseDto, PhysicalWarehouseDetailResponseDto } from '../../dto/warehouse';
import { AdminPhysicalWarehouseController } from '../../controllers';
import { WarehouseResponseDto } from '../../dto/warehouse';
import { CustomFieldValueResponseDto } from '../../dto/warehouse';

describe('AdminPhysicalWarehouseController (e2e)', () => {
  let app: INestApplication;
  let adminPhysicalWarehouseService: AdminPhysicalWarehouseService;

  const mockAdminPhysicalWarehouseService = {
    findAll: jest.fn() as jest.Mock,
    findOne: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AdminPhysicalWarehouseController],
      providers: [
        {
          provide: AdminPhysicalWarehouseService,
          useValue: mockAdminPhysicalWarehouseService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    adminPhysicalWarehouseService = moduleFixture.get<AdminPhysicalWarehouseService>(AdminPhysicalWarehouseService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/physical-warehouses', () => {
    it('nên trả về danh sách kho vật lý phân trang', async () => {
      // Arrange
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: 1,
        name: 'Kho chính Hà Nội',
        description: 'Kho chứa hàng hóa chính tại Hà Nội',
        type: WarehouseTypeEnum.PHYSICAL
      };

      const mockPhysicalWarehouses: PhysicalWarehouseResponseDto[] = [
        {
          warehouseId: 1,
          address: 'Số 123 Đường Láng, Đống Đa, Hà Nội',
          capacity: 3000,
          warehouse: mockWarehouseInfo
        },
        {
          warehouseId: 3,
          address: 'Số 45 Phố Huế, Hai Bà Trưng, Hà Nội',
          capacity: 2500,
          warehouse: {
            ...mockWarehouseInfo,
            warehouseId: 3,
            name: 'Kho phụ Hà Nội'
          }
        }
      ];

      const mockPaginatedResult: PaginatedResult<PhysicalWarehouseResponseDto> = {
        items: mockPhysicalWarehouses,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 2,
          totalItems: 2,
          totalPages: 1,
        },
      };

      mockAdminPhysicalWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/physical-warehouses')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách kho vật lý thành công');
          expect(res.body.result.items).toHaveLength(2);
          expect(res.body.result.items[0].warehouseId).toBe(1);
          expect(res.body.result.items[0].address).toBe('Số 123 Đường Láng, Đống Đa, Hà Nội');
          expect(res.body.result.items[0].warehouse.name).toBe('Kho chính Hà Nội');
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(2);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<PhysicalWarehouseResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockAdminPhysicalWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/physical-warehouses')
        .query({
          page: 2,
          limit: 5,
          address: 'Hà Nội',
          minCapacity: 1000,
          maxCapacity: 5000
        })
        .expect(200)
        .expect(() => {
          const findAllMock = adminPhysicalWarehouseService.findAll as jest.Mock;
          const lastCall = findAllMock.mock.calls[findAllMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            address: 'Hà Nội',
            minCapacity: '1000', // Query params are strings
            maxCapacity: '5000', // Query params are strings
          }));
        });
    });
  });

  describe('GET /admin/physical-warehouses/:warehouseId', () => {
    it('nên trả về thông tin chi tiết kho vật lý theo ID', async () => {
      // Arrange
      const warehouseId = 1;
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: 1,
        name: 'Kho chính Hà Nội',
        description: 'Kho chứa hàng hóa chính tại Hà Nội',
        type: WarehouseTypeEnum.PHYSICAL
      };

      const mockCustomField: CustomFieldValueResponseDto = {
        fieldId: 3,
        label: 'Khu vực',
        type: 'TEXT',
        value: {
          value: 'Miền Bắc'
        }
      };

      const mockPhysicalWarehouseDetail: PhysicalWarehouseDetailResponseDto = {
        warehouseId: 1,
        address: 'Số 123 Đường Láng, Đống Đa, Hà Nội',
        capacity: 3000,
        warehouse: mockWarehouseInfo,
        customFields: [mockCustomField]
      };

      mockAdminPhysicalWarehouseService.findOne.mockResolvedValue(mockPhysicalWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/physical-warehouses/${warehouseId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết kho vật lý thành công');
          expect(res.body.result.warehouseId).toBe(1);
          expect(res.body.result.address).toBe('Số 123 Đường Láng, Đống Đa, Hà Nội');
          expect(res.body.result.capacity).toBe(3000);
          expect(res.body.result.warehouse).toBeDefined();
          expect(res.body.result.warehouse.name).toBe('Kho chính Hà Nội');
          expect(res.body.result.customFields).toHaveLength(1);
          expect(res.body.result.customFields[0].fieldId).toBe(3);
          expect(res.body.result.customFields[0].label).toBe('Khu vực');
          expect(res.body.result.customFields[0].value.value).toBe('Miền Bắc');
        });
    });

    it('nên truyền ID kho đúng cho service', async () => {
      // Arrange
      const warehouseId = 123;
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: warehouseId,
        name: 'Kho chính Hà Nội',
        description: 'Kho chứa hàng hóa chính tại Hà Nội',
        type: WarehouseTypeEnum.PHYSICAL
      };

      const mockPhysicalWarehouseDetail: PhysicalWarehouseDetailResponseDto = {
        warehouseId: warehouseId,
        address: 'Số 123 Đường Láng, Đống Đa, Hà Nội',
        capacity: 3000,
        warehouse: mockWarehouseInfo,
        customFields: []
      };

      mockAdminPhysicalWarehouseService.findOne.mockResolvedValue(mockPhysicalWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/physical-warehouses/${warehouseId}`)
        .expect(200)
        .expect(() => {
          const findOneMock = adminPhysicalWarehouseService.findOne as jest.Mock;
          expect(findOneMock).toHaveBeenCalledWith(expect.any(String));
          // Kiểm tra xem tham số được truyền vào có chứa giá trị warehouseId không
          const lastCall = findOneMock.mock.calls[findOneMock.mock.calls.length - 1];
          expect(lastCall[0]).toBe(String(warehouseId));
        });
    });
  });
});
