import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { WarehouseTypeEnum } from '../../../enums';
import { AdminVirtualWarehouseService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { VirtualWarehouseResponseDto, VirtualWarehouseDetailResponseDto } from '../../dto/warehouse';
import { AdminVirtualWarehouseController } from '../../controllers';
import { WarehouseResponseDto } from '../../dto/warehouse';
import { CustomFieldValueResponseDto } from '../../dto/warehouse';

describe('AdminVirtualWarehouseController (e2e)', () => {
  let app: INestApplication;
  let adminVirtualWarehouseService: AdminVirtualWarehouseService;

  const mockAdminVirtualWarehouseService = {
    findAll: jest.fn() as jest.Mock,
    findOne: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AdminVirtualWarehouseController],
      providers: [
        {
          provide: AdminVirtualWarehouseService,
          useValue: mockAdminVirtualWarehouseService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    adminVirtualWarehouseService = moduleFixture.get<AdminVirtualWarehouseService>(AdminVirtualWarehouseService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/virtual-warehouses', () => {
    it('nên trả về danh sách kho ảo phân trang', async () => {
      // Arrange
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: 2,
        name: 'Kho ảo ERP',
        description: 'Kho ảo quản lý hàng hóa trực tuyến qua hệ thống ERP',
        type: WarehouseTypeEnum.VIRTUAL
      };

      const mockVirtualWarehouses: VirtualWarehouseResponseDto[] = [
        {
          warehouseId: 2,
          associatedSystem: 'ERP System',
          purpose: 'Digital inventory management',
          warehouse: mockWarehouseInfo
        },
        {
          warehouseId: 4,
          associatedSystem: 'ERP Cloud',
          purpose: 'Cloud inventory tracking',
          warehouse: {
            ...mockWarehouseInfo,
            warehouseId: 4,
            name: 'Kho ảo Cloud'
          }
        }
      ];

      const mockPaginatedResult: PaginatedResult<VirtualWarehouseResponseDto> = {
        items: mockVirtualWarehouses,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 2,
          totalItems: 2,
          totalPages: 1,
        },
      };

      mockAdminVirtualWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/virtual-warehouses')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách kho ảo thành công');
          expect(res.body.result.items).toHaveLength(2);
          expect(res.body.result.items[0].warehouseId).toBe(2);
          expect(res.body.result.items[0].associatedSystem).toBe('ERP System');
          expect(res.body.result.items[0].warehouse.name).toBe('Kho ảo ERP');
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(2);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<VirtualWarehouseResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockAdminVirtualWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/virtual-warehouses')
        .query({
          page: 2,
          limit: 5,
          associatedSystem: 'ERP'
        })
        .expect(200)
        .expect(() => {
          const findAllMock = adminVirtualWarehouseService.findAll as jest.Mock;
          const lastCall = findAllMock.mock.calls[findAllMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            associatedSystem: 'ERP'
          }));
        });
    });
  });

  describe('GET /admin/virtual-warehouses/:warehouseId', () => {
    it('nên trả về thông tin chi tiết kho ảo theo ID', async () => {
      // Arrange
      const warehouseId = 2;
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: 2,
        name: 'Kho ảo ERP',
        description: 'Kho ảo quản lý hàng hóa trực tuyến qua hệ thống ERP',
        type: WarehouseTypeEnum.VIRTUAL
      };

      const mockCustomField: CustomFieldValueResponseDto = {
        fieldId: 5,
        label: 'API Version',
        type: 'TEXT',
        value: {
          value: 'v2.1'
        }
      };

      const mockVirtualWarehouseDetail: VirtualWarehouseDetailResponseDto = {
        warehouseId: 2,
        associatedSystem: 'ERP System',
        purpose: 'Digital inventory management',
        warehouse: mockWarehouseInfo,
        customFields: [mockCustomField]
      };

      mockAdminVirtualWarehouseService.findOne.mockResolvedValue(mockVirtualWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/virtual-warehouses/${warehouseId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết kho ảo thành công');
          expect(res.body.result.warehouseId).toBe(2);
          expect(res.body.result.associatedSystem).toBe('ERP System');
          expect(res.body.result.purpose).toBe('Digital inventory management');
          expect(res.body.result.warehouse).toBeDefined();
          expect(res.body.result.warehouse.name).toBe('Kho ảo ERP');
          expect(res.body.result.customFields).toHaveLength(1);
          expect(res.body.result.customFields[0].fieldId).toBe(5);
          expect(res.body.result.customFields[0].label).toBe('API Version');
          expect(res.body.result.customFields[0].value.value).toBe('v2.1');
        });
    });

    it('nên truyền ID kho đúng cho service', async () => {
      // Arrange
      const warehouseId = 123;
      const mockWarehouseInfo: WarehouseResponseDto = {
        warehouseId: warehouseId,
        name: 'Kho ảo ERP',
        description: 'Kho ảo quản lý hàng hóa trực tuyến',
        type: WarehouseTypeEnum.VIRTUAL
      };

      const mockVirtualWarehouseDetail: VirtualWarehouseDetailResponseDto = {
        warehouseId: warehouseId,
        associatedSystem: 'ERP System',
        purpose: 'Digital inventory management',
        warehouse: mockWarehouseInfo,
        customFields: []
      };

      mockAdminVirtualWarehouseService.findOne.mockResolvedValue(mockVirtualWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/virtual-warehouses/${warehouseId}`)
        .expect(200)
        .expect(() => {
          const findOneMock = adminVirtualWarehouseService.findOne as jest.Mock;
          expect(findOneMock).toHaveBeenCalledWith(expect.any(String));
          // Kiểm tra xem tham số được truyền vào có chứa giá trị warehouseId không
          const lastCall = findOneMock.mock.calls[findOneMock.mock.calls.length - 1];
          expect(lastCall[0]).toBe(String(warehouseId));
        });
    });
  });
});
