import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { WarehouseTypeEnum } from '../../../enums';
import { AdminWarehouseService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { WarehouseResponseDto, WarehouseDetailResponseDto } from '../../dto/warehouse';
import { AdminWarehouseController } from '../../controllers';
import { CustomFieldValueResponseDto } from '../../dto/warehouse';

describe('AdminWarehouseController (e2e)', () => {
  let app: INestApplication;
  let adminWarehouseService: AdminWarehouseService;

  const mockAdminWarehouseService = {
    findAll: jest.fn(),
    findOne: jest.fn(),
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [AdminWarehouseController],
      providers: [
        {
          provide: AdminWarehouseService,
          useValue: mockAdminWarehouseService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    adminWarehouseService = moduleFixture.get<AdminWarehouseService>(AdminWarehouseService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/warehouses', () => {
    it('nên trả về danh sách kho phân trang', async () => {
      // Arrange
      const mockWarehouses: WarehouseResponseDto[] = [
        {
          warehouseId: 1,
          name: 'Kho chính',
          description: 'Kho chứa hàng hóa chính của công ty',
          type: WarehouseTypeEnum.PHYSICAL
        },
        {
          warehouseId: 2,
          name: 'Kho ảo',
          description: 'Kho ảo quản lý hàng hóa trực tuyến',
          type: WarehouseTypeEnum.VIRTUAL
        }
      ];

      const mockPaginatedResult: PaginatedResult<WarehouseResponseDto> = {
        items: mockWarehouses,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 2,
          totalItems: 2,
          totalPages: 1,
        },
      };

      mockAdminWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/warehouses')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách kho thành công');
          expect(res.body.result.items).toHaveLength(2);
          expect(res.body.result.items[0].warehouseId).toBe(1);
          expect(res.body.result.items[0].name).toBe('Kho chính');
          expect(res.body.result.items[0].type).toBe(WarehouseTypeEnum.PHYSICAL);
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(2);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<WarehouseResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockAdminWarehouseService.findAll.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/warehouses')
        .query({
          page: 2,
          limit: 5,
          type: WarehouseTypeEnum.PHYSICAL,
          search: 'Kho chính'
        })
        .expect(200)
        .expect(() => {
          const findAllMock = adminWarehouseService.findAll as jest.Mock;
          const lastCall = findAllMock.mock.calls[findAllMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            type: WarehouseTypeEnum.PHYSICAL,
            search: 'Kho chính'
          }));
        });
    });
  });

  describe('GET /admin/warehouses/:warehouseId', () => {
    it('nên trả về thông tin chi tiết kho theo ID', async () => {
      // Arrange
      const warehouseId = 1;
      const mockCustomField: CustomFieldValueResponseDto = {
        fieldId: 3,
        label: 'Khu vực',
        type: 'TEXT',
        value: {
          value: 'North'
        }
      };

      const mockWarehouseDetail: WarehouseDetailResponseDto = {
        warehouseId: 1,
        name: 'Kho chính',
        description: 'Kho chứa hàng hóa chính của công ty',
        type: WarehouseTypeEnum.PHYSICAL,
        details: {
          address: '123 Storage St, Warehouse City',
          capacity: 5000
        },
        customFields: [mockCustomField],
        getDetailsType: jest.fn()
      };

      mockAdminWarehouseService.findOne.mockResolvedValue(mockWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/warehouses/${warehouseId}`)
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết kho thành công');
          expect(res.body.result.warehouseId).toBe(1);
          expect(res.body.result.name).toBe('Kho chính');
          expect(res.body.result.type).toBe(WarehouseTypeEnum.PHYSICAL);
          expect(res.body.result.details).toBeDefined();
          expect(res.body.result.details.address).toBe('123 Storage St, Warehouse City');
          expect(res.body.result.customFields).toHaveLength(1);
          expect(res.body.result.customFields[0].fieldId).toBe(3);
          expect(res.body.result.customFields[0].label).toBe('Khu vực');
          expect(res.body.result.customFields[0].value.value).toBe('North');
        });
    });

    it('nên truyền ID kho đúng cho service', async () => {
      // Arrange
      const warehouseId = 123;
      const mockWarehouseDetail: WarehouseDetailResponseDto = {
        warehouseId: warehouseId,
        name: 'Kho chính',
        description: 'Kho chứa hàng hóa chính của công ty',
        type: WarehouseTypeEnum.PHYSICAL,
        details: {
          address: '123 Storage St, Warehouse City',
          capacity: 5000
        },
        customFields: [],
        getDetailsType: jest.fn()
      };

      mockAdminWarehouseService.findOne.mockResolvedValue(mockWarehouseDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/warehouses/${warehouseId}`)
        .expect(200)
        .expect(() => {
          const findOneMock = adminWarehouseService.findOne as jest.Mock;
          expect(findOneMock).toHaveBeenCalledWith(expect.any(String));
          // Kiểm tra xem tham số được truyền vào có chứa giá trị warehouseId không
          const lastCall = findOneMock.mock.calls[findOneMock.mock.calls.length - 1];
          expect(lastCall[0]).toBe(String(warehouseId));
        });
    });
  });
});
