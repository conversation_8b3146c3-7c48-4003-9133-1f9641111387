import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { EntityStatusEnum } from '../../../enums';
import { PriceTypeEnum } from '../../../enums';
import { CustomFieldStatus } from '../../dto';
import { ProductAdminService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { UserProductResponseDto } from '../../dto';
import { UserProductDetailResponseDto } from '../../dto';
import { ProductAdminController } from '../../controllers';

describe('ProductAdminController (e2e)', () => {
  let app: INestApplication;
  let productAdminService: ProductAdminService;

  const mockProductAdminService = {
    getProducts: jest.fn() as jest.Mock,
    getProductById: jest.fn() as jest.Mock,
    updateProductStatus: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [ProductAdminController],
      providers: [
        {
          provide: ProductAdminService,
          useValue: mockProductAdminService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    productAdminService = moduleFixture.get<ProductAdminService>(ProductAdminService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/user-products', () => {
    it('nên trả về danh sách sản phẩm phân trang', async () => {
      // Arrange
      const mockProducts: UserProductResponseDto[] = [
        {
          id: 1,
          name: 'Sản phẩm A',
          price: { listPrice: 1000, salePrice: 900, currency: 'VND' },
          typePrice: PriceTypeEnum.HAS_PRICE,
          description: 'Mô tả sản phẩm A',
          images: [
            { key: 'products/image1.jpg', url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
          ],
          tags: ['tag1', 'tag2'],
          createdBy: 123,
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
        },
      ];

      const mockPaginatedResult: PaginatedResult<UserProductResponseDto> = {
        items: mockProducts,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 1,
          totalItems: 1,
          totalPages: 1,
        },
      };

      mockProductAdminService.getProducts.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/user-products')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách sản phẩm thành công');
          expect(res.body.result.items).toHaveLength(1);
          expect(res.body.result.items[0].id).toBe(1);
          expect(res.body.result.items[0].name).toBe('Sản phẩm A');
          expect(res.body.result.meta.currentPage).toBe(1);
          expect(res.body.result.meta.totalItems).toBe(1);
        });
    });

    it('nên truyền các tham số truy vấn đúng cho service', async () => {
      // Arrange
      const mockPaginatedResult: PaginatedResult<UserProductResponseDto> = {
        items: [],
        meta: {
          currentPage: 2,
          itemsPerPage: 5,
          itemCount: 0,
          totalItems: 0,
          totalPages: 0,
        },
      };

      mockProductAdminService.getProducts.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/user-products')
        .query({
          page: 2,
          limit: 5,
          search: 'test',
          status: EntityStatusEnum.PENDING,
          userId: 123,
        })
        .expect(200)
        .expect(() => {
          const getProductsMock = productAdminService.getProducts as jest.Mock;
          const lastCall = getProductsMock.mock.calls[getProductsMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.any(Number));
          expect(lastCall[1]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            search: 'test',
            status: EntityStatusEnum.PENDING,
            userId: '123', // Query params are strings
          }));
        });
    });
  });

  describe('GET /admin/user-products/:id', () => {
    it('nên trả về thông tin chi tiết sản phẩm theo ID', async () => {
      // Arrange
      const mockProductDetail: UserProductDetailResponseDto = {
        id: 1,
        name: 'Sản phẩm A',
        price: { listPrice: 1000, salePrice: 900, currency: 'VND' },
        typePrice: PriceTypeEnum.HAS_PRICE,
        description: 'Mô tả sản phẩm A',
        images: [
          { key: 'products/image1.jpg', url: 'https://cdn.redai.vn/products/image1.jpg', position: 0 },
        ],
        tags: ['tag1', 'tag2'],
        createdBy: 123,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        classifications: null,
        customGroupForms: [{
          id: 1,
          label: 'Thông tin chi tiết',
          fields: [
            {
              id: 1,
              component: 'input',
              configId: 'product_color',
              label: 'Màu sắc',
              type: 'text',
              required: true,
              configJson: {
                placeholder: 'Nhập màu sắc',
                maxLength: 50,
              },
              employeeId: 1,
              userId: null,
              createAt: 1625097600000,
              status: CustomFieldStatus.APPROVED,
              value: { value: 'Đỏ' },
            },
          ],
        }],
      };

      mockProductAdminService.getProductById.mockResolvedValue(mockProductDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/user-products/1')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy thông tin chi tiết sản phẩm thành công');
          expect(res.body.result.id).toBe(1);
          expect(res.body.result.name).toBe('Sản phẩm A');
          expect(res.body.result.customGroupForms).toBeDefined();
          expect(res.body.result.customGroupForms[0].id).toBe(1);
          expect(res.body.result.customGroupForms[0].fields).toHaveLength(1);
        });
    });

    it('nên truyền ID sản phẩm đúng cho service', async () => {
      // Arrange
      const productId = 123;
      const mockProductDetail: UserProductDetailResponseDto = {
        id: productId,
        name: 'Sản phẩm A',
        price: { listPrice: 1000, salePrice: 900, currency: 'VND' },
        typePrice: PriceTypeEnum.HAS_PRICE,
        description: 'Mô tả sản phẩm A',
        images: [],
        tags: [],
        createdBy: 123,
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        customGroupForms: [],
        classifications: null,
      };

      mockProductAdminService.getProductById.mockResolvedValue(mockProductDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/user-products/${productId}`)
        .expect(200)
        .expect(() => {
          const getProductByIdMock = productAdminService.getProductById as jest.Mock;
          expect(getProductByIdMock).toHaveBeenCalledWith(
            expect.any(Number),
            productId,
          );
        });
    });
  });

  describe('POST /admin/user-products/status', () => {
    it('nên cập nhật trạng thái sản phẩm thành công', async () => {
      // Arrange
      const updateStatusDto = {
        productIds: [1, 2, 3],
        status: EntityStatusEnum.APPROVED,
      };

      const updatedCount = 3;
      mockProductAdminService.updateProductStatus.mockResolvedValue(updatedCount);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/admin/user-products/status')
        .send(updateStatusDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.code).toBe(200); // API response code is still 200 even though HTTP status is 201
          expect(res.body.message).toBe('Cập nhật trạng thái sản phẩm thành công');
          expect(res.body.result.updatedCount).toBe(3);
        });
    });

    it('nên truyền dữ liệu cập nhật đúng cho service', async () => {
      // Arrange
      const updateStatusDto = {
        productIds: [1, 2, 3],
        status: EntityStatusEnum.REJECTED,
        rejectReason: 'Sản phẩm không đáp ứng tiêu chuẩn chất lượng',
      };

      mockProductAdminService.updateProductStatus.mockResolvedValue(3);

      // Act & Assert
      return request(app.getHttpServer())
        .post('/admin/user-products/status')
        .send(updateStatusDto)
        .expect(201)
        .expect(() => {
          const updateProductStatusMock = productAdminService.updateProductStatus as jest.Mock;
          expect(updateProductStatusMock).toHaveBeenCalledWith(
            expect.any(Number),
            expect.objectContaining({
              productIds: [1, 2, 3],
              status: EntityStatusEnum.REJECTED,
              rejectReason: 'Sản phẩm không đáp ứng tiêu chuẩn chất lượng',
            }),
          );
        });
    });
  });
});
