import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { JwtEmployeeGuard } from '../../../../auth/guards';
import { PermissionsGuard } from '../../../../auth/guards/permissions.guard';
import { UserOrderAdminService } from '../../services';
import { PaginatedResult } from '../../../../../common/response';
import { UserOrderResponseDto, UserOrderDetailResponseDto } from '../../dto';

describe('UserOrderAdminController (e2e)', () => {
  let app: INestApplication;
  let userOrderAdminService: UserOrderAdminService;

  const mockUserOrderAdminService = {
    getUserOrders: jest.fn() as jest.Mock,
    getUserOrderById: jest.fn() as jest.Mock,
  };

  const mockJwtEmployeeGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  const mockPermissionsGuard = {
    canActivate: jest.fn().mockImplementation(() => true),
  };

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      controllers: [],
      providers: [
        {
          provide: UserOrderAdminService,
          useValue: mockUserOrderAdminService
        }
      ]
    })
      .overrideGuard(JwtEmployeeGuard)
      .useValue(mockJwtEmployeeGuard)
      .overrideGuard(PermissionsGuard)
      .useValue(mockPermissionsGuard)
      .compile();

    app = moduleFixture.createNestApplication();
    userOrderAdminService = moduleFixture.get<UserOrderAdminService>(UserOrderAdminService);

    // Thêm middleware giả lập request.employee
    app.use((req, res, next) => {
      req.employee = { id: 1, email: '<EMAIL>', role: 'admin' };
      next();
    });

    // Thiết lập mock routes
    app.use('/admin/business/user-orders/:id', (req, res, next) => {
      if (req.method === 'GET' && req.params.id) {
        const id = parseInt(req.params.id, 10);
        mockUserOrderAdminService.getUserOrderById(req.employee.id, id)
          .then((result: UserOrderDetailResponseDto) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy chi tiết đơn hàng thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    app.use('/admin/business/user-orders', (req, res, next) => {
      if (req.method === 'GET') {
        const queryParams = req.query;
        mockUserOrderAdminService.getUserOrders(req.employee.id, queryParams)
          .then((result: PaginatedResult<UserOrderResponseDto>) => {
            res.status(200).json({
              code: 200,
              message: 'Lấy danh sách đơn hàng thành công',
              result
            });
          })
          .catch((err: any) => {
            res.status(500).json({
              code: 500,
              message: err.message,
              errors: err.errors
            });
          });
      } else {
        next();
      }
    });

    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('GET /admin/business/user-orders', () => {
    it('nên trả về danh sách đơn hàng phân trang', async () => {
      // Arrange
      const mockOrders: UserOrderResponseDto[] = [
        {
          id: 1,
          userConvertCustomerId: 2,
          userId: 3,
          productInfo: [
            {
              productId: 1,
              name: 'Sản phẩm A',
              quantity: 2,
              price: 100000,
            },
          ],
          billInfo: {
            subtotal: 200000,
            total: 200000,
            paymentMethod: 'COD',
          },
          hasShipping: true,
          shippingStatus: 'pending',
          logisticInfo: {
            address: 'Hà Nội',
          },
          createdAt: 1625097600000,
          updatedAt: 1625184000000,
          source: 'website',
        },
      ];

      const mockPaginatedResult: PaginatedResult<UserOrderResponseDto> = {
        items: mockOrders,
        meta: {
          currentPage: 1,
          itemsPerPage: 10,
          itemCount: 1,
          totalItems: 1,
          totalPages: 1,
        },
      };

      mockUserOrderAdminService.getUserOrders.mockResolvedValue(mockPaginatedResult);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-orders')
        .query({
          page: 2,
          limit: 5,
          search: 'test',
          userId: 3,
          userConvertCustomerId: 2,
          hasShipping: true,
          shippingStatus: 'pending',
        })
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy danh sách đơn hàng thành công');
          expect(res.body.result.items).toHaveLength(1);
          expect(res.body.result.items[0].id).toBe(1);
          expect(res.body.result.items[0].hasShipping).toBe(true);

          const getUserOrdersMock = userOrderAdminService.getUserOrders as jest.Mock;
          const lastCall = getUserOrdersMock.mock.calls[getUserOrdersMock.mock.calls.length - 1];
          expect(lastCall[0]).toEqual(expect.any(Number));
          expect(lastCall[1]).toEqual(expect.objectContaining({
            page: '2', // Query params are strings
            limit: '5', // Query params are strings
            search: 'test',
            userId: '3', // Query params are strings
            userConvertCustomerId: '2', // Query params are strings
            hasShipping: 'true', // Query params are strings
            shippingStatus: 'pending',
          }));
        });
    });
  });

  describe('GET /admin/business/user-orders/:id', () => {
    it('nên trả về thông tin chi tiết đơn hàng theo ID', async () => {
      // Arrange
      const mockOrderDetail: UserOrderDetailResponseDto = {
        id: 1,
        userConvertCustomerId: 2,
        userId: 3,
        productInfo: [
          {
            productId: 1,
            name: 'Sản phẩm A',
            quantity: 2,
            price: 100000,
          },
        ],
        billInfo: {
          subtotal: 200000,
          total: 200000,
          paymentMethod: 'COD',
        },
        hasShipping: true,
        shippingStatus: 'pending',
        logisticInfo: {
          address: 'Hà Nội',
        },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        source: 'website',
        customer: {
          id: 2,
          avatar: 'avatars/customer-123.jpg',
          name: 'Nguyễn Văn A',
          email: { primary: '<EMAIL>' },
          phone: '0912345678',
          platform: 'Facebook',
          timezone: 'Asia/Ho_Chi_Minh',
          createdAt: 1625097600000,
          updatedAt: 1625097600000,
          userId: 3,
          agentId: '550e8400-e29b-41d4-a716-446655440000',
          metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }]
        }
      };

      mockUserOrderAdminService.getUserOrderById.mockResolvedValue(mockOrderDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get('/admin/business/user-orders/1')
        .expect(200)
        .expect((res) => {
          expect(res.body.code).toBe(200);
          expect(res.body.message).toBe('Lấy chi tiết đơn hàng thành công');
          expect(res.body.result.id).toBe(1);
          expect(res.body.result.hasShipping).toBe(true);
          expect(res.body.result.customer).toBeDefined();
          expect(res.body.result.customer.id).toBe(2);
          expect(res.body.result.customer.name).toBe('Nguyễn Văn A');
        });
    });

    it('nên truyền ID đơn hàng đúng cho service', async () => {
      // Arrange
      const orderId = 123;
      const mockOrderDetail: UserOrderDetailResponseDto = {
        id: orderId,
        userConvertCustomerId: 2,
        userId: 3,
        productInfo: [
          {
            productId: 1,
            name: 'Sản phẩm A',
            quantity: 2,
            price: 100000,
          },
        ],
        billInfo: {
          subtotal: 200000,
          total: 200000,
          paymentMethod: 'COD',
        },
        hasShipping: true,
        shippingStatus: 'pending',
        logisticInfo: {
          address: 'Hà Nội',
        },
        createdAt: 1625097600000,
        updatedAt: 1625184000000,
        source: 'website',
        customer: null
      };

      mockUserOrderAdminService.getUserOrderById.mockResolvedValue(mockOrderDetail);

      // Act & Assert
      return request(app.getHttpServer())
        .get(`/admin/business/user-orders/${orderId}`)
        .expect(200)
        .expect(() => {
          const getUserOrderByIdMock = userOrderAdminService.getUserOrderById as jest.Mock;
          expect(getUserOrderByIdMock).toHaveBeenCalledWith(expect.any(Number), orderId);
        });
    });
  });
});
