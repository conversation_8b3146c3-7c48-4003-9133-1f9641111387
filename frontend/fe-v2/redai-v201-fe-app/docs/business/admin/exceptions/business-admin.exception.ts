import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions/business.exception';

/**
 * Mã lỗi cho module Business Admin
 * Kế thừa từ BUSINESS_ERROR_CODES và bổ sung thêm các mã lỗi đặc thù cho admin
 */
export const BUSINESS_ADMIN_ERROR_CODES = {
  ...BUSINESS_ERROR_CODES,

  // Custom Field Admin exceptions (30101-30120)
  CUSTOM_FIELD_NOT_FOUND: new ErrorCode(
    30101,
    'Không tìm thấy trường tùy chỉnh',
    HttpStatus.NOT_FOUND
  ),
  CUSTOM_FIELD_CREATION_ERROR: new ErrorCode(
    30102,
    'Lỗi khi tạo trường tùy chỉnh',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  CUSTOM_FIELD_UPDATE_ERROR: new ErrorCode(
    30103,
    'Lỗi khi cập nhật trường tùy chỉnh',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  CUSTOM_FIELD_FETCH_ERROR: new ErrorCode(
    30104,
    'Lỗi khi lấy danh sách trường tùy chỉnh',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  CONFIG_ID_DUPLICATE: new ErrorCode(
    30105,
    'Config ID đã tồn tại',
    HttpStatus.CONFLICT
  ),
  CUSTOM_FIELD_VALIDATION_ERROR: new ErrorCode(
    30106,
    'Dữ liệu trường tùy chỉnh không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  // User Product Admin exceptions (30121-30140)
  PRODUCT_FETCH_ERROR: new ErrorCode(
    30121,
    'Lỗi khi lấy danh sách sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_DETAIL_FETCH_ERROR: new ErrorCode(
    30122,
    'Lỗi khi lấy chi tiết sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  USER_PRODUCT_NOT_FOUND: new ErrorCode(
    30123,
    'Không tìm thấy sản phẩm của người dùng',
    HttpStatus.NOT_FOUND
  ),
  PRODUCT_STATUS_UPDATE_ERROR: new ErrorCode(
    30124,
    'Lỗi khi cập nhật trạng thái sản phẩm',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  PRODUCT_STATUS_INVALID_TRANSITION: new ErrorCode(
    30125,
    'Chuyển đổi trạng thái sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),
  PRODUCT_REJECT_REASON_REQUIRED: new ErrorCode(
    30126,
    'Cần cung cấp lý do từ chối khi chuyển trạng thái sang REJECTED',
    HttpStatus.BAD_REQUEST
  ),
  PRODUCT_PRICE_VALIDATION_ERROR: new ErrorCode(
    30127,
    'Cấu trúc giá sản phẩm không hợp lệ',
    HttpStatus.BAD_REQUEST
  ),

  // User Convert Admin exceptions (30141-30160)
  USER_CONVERT_NOT_FOUND: new ErrorCode(
    30141,
    'Không tìm thấy bản ghi chuyển đổi khách hàng',
    HttpStatus.NOT_FOUND
  ),
  USER_CONVERT_FETCH_ERROR: new ErrorCode(
    30142,
    'Lỗi khi lấy danh sách bản ghi chuyển đổi khách hàng',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // User Convert Customer Admin exceptions (30161-30180)
  USER_CONVERT_CUSTOMER_NOT_FOUND: new ErrorCode(
    30161,
    'Không tìm thấy khách hàng chuyển đổi',
    HttpStatus.NOT_FOUND
  ),
  USER_CONVERT_CUSTOMER_FETCH_ERROR: new ErrorCode(
    30162,
    'Lỗi khi lấy danh sách khách hàng chuyển đổi',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // User Order Admin exceptions (30181-30200)
  USER_ORDER_NOT_FOUND: new ErrorCode(
    30181,
    'Không tìm thấy đơn hàng',
    HttpStatus.NOT_FOUND
  ),
  USER_ORDER_FETCH_ERROR: new ErrorCode(
    30182,
    'Lỗi khi lấy danh sách đơn hàng',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),

  // General exceptions
  GENERAL_ERROR: new ErrorCode(
    30999,
    'Lỗi chung của module business',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
};
