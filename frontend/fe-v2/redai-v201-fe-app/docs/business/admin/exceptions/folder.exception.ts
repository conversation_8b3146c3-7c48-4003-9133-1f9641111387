import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module Folder
 * Range: 30550-30599
 */
export const FOLDER_ERROR_CODES = {
  // Folder exceptions
  FOLDER_NOT_FOUND: new ErrorCode(
    30550,
    'Không tìm thấy thư mục',
    HttpStatus.NOT_FOUND
  ),
  FOLDER_FIND_FAILED: new ErrorCode(
    30551,
    'Lỗi khi tìm kiếm thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  FOLDER_DETAIL_FAILED: new ErrorCode(
    30552,
    'Lỗi khi lấy chi tiết thư mục',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  FOLDER_UNAUTHORIZED: new ErrorCode(
    30553,
    'Không có quyền truy cập thư mục này',
    HttpStatus.FORBIDDEN
  ),
};
