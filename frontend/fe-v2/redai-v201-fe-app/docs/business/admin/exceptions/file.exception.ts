import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Mã lỗi cho module File
 * Range: 30500-30549
 */
export const FILE_ERROR_CODES = {
  // File exceptions
  FILE_NOT_FOUND: new ErrorCode(
    30500,
    'Không tìm thấy tệp tin',
    HttpStatus.NOT_FOUND
  ),
  FILE_FIND_FAILED: new ErrorCode(
    30501,
    'Lỗi khi tìm kiếm tệp tin',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  FILE_DETAIL_FAILED: new ErrorCode(
    30502,
    'Lỗi khi lấy chi tiết tệp tin',
    HttpStatus.INTERNAL_SERVER_ERROR
  ),
  FILE_UNAUTHORIZED: new ErrorCode(
    30503,
    'Không có quyền truy cập tệp tin này',
    HttpStatus.FORBIDDEN
  ),
};
