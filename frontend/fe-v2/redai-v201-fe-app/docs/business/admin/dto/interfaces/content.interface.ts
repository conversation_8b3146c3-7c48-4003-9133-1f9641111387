import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * Class cho dữ liệu nội dung bổ sung của bản ghi chuyển đổi
 */
export class ConvertContent {
  /**
   * Thông tin bổ sung
   */
  @ApiProperty({
    description: 'Thông tin bổ sung',
    example: 'Thông tin thêm về khách hàng',
    required: false
  })
  @IsOptional()
  @IsString()
  additionalInfo?: string;

  /**
   * Nguồn tham chiếu
   */
  @ApiProperty({
    description: 'Nguồn tham chiếu',
    example: 'Facebook',
    required: false
  })
  @IsOptional()
  @IsString()
  referenceSource?: string;

  /**
   * Dữ liệu tùy chỉnh khác
   */
  [key: string]: any;
}
