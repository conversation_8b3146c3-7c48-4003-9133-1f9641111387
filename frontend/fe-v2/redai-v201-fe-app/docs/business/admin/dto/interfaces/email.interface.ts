import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

/**
 * Class cho dữ liệu email của khách hàng
 */
export class EmailData {
  /**
   * Email chính
   */
  @ApiProperty({
    description: 'Email chính',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsString()
  primary?: string;

  /**
   * Email phụ (nếu có)
   */
  @ApiProperty({
    description: 'Email phụ',
    example: '<EMAIL>',
    required: false
  })
  @IsOptional()
  @IsString()
  secondary?: string;
}
