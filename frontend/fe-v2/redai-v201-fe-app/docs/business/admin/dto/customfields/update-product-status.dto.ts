import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { EntityStatusEnum } from '../../../enums/entity-status.enum';

/**
 * DTO cho việc cập nhật trạng thái sản phẩm
 */
export class UpdateProductStatusDto {
  @ApiProperty({
    description: 'Danh sách ID sản phẩm cần cập nhật trạng thái',
    type: [Number],
    example: [1, 2, 3],
  })
  @IsArray({ message: 'productIds phải là một mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi ID sản phẩm phải là số' })
  @Type(() => Number)
  productIds: number[];

  @ApiProperty({
    description: 'Trạng thái mới của sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
  })
  @IsEnum(EntityStatusEnum, { message: 'Trạng thái không hợp lệ' })
  status: EntityStatusEnum;

  @ApiProperty({
    description: 'Lý do từ chối (bắt buộc khi trạng thái là REJECTED)',
    example: 'Sản phẩm không đáp ứng tiêu chuẩn chất lượng',
    required: false,
  })
  @IsString({ message: 'Lý do từ chối phải là chuỗi' })
  @IsOptional()
  rejectReason?: string;
}
