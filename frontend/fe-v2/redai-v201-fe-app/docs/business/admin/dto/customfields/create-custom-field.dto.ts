import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsObject, IsString, Matches } from 'class-validator';

/**
 * DTO cho việc tạo trường tùy chỉnh mới
 */
export class CreateCustomFieldDto {
  @ApiProperty({
    description: 'Thành phần UI',
    example: 'input',
  })
  @IsNotEmpty()
  @IsString()
  component: string;

  @ApiProperty({
    description: 'ID cấu hình (phải là unique)',
    example: 'product_color',
  })
  @IsNotEmpty()
  @IsString()
  configId: string;

  @ApiProperty({
    description: 'Nhãn hiển thị',
    example: 'Màu sắc',
  })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({
    description: 'Loại trường',
    example: 'text',
  })
  @IsNotEmpty()
  @IsString()
  type: string;

  @ApiProperty({
    description: 'Trường bắt buộc hay không',
    example: true,
  })
  @IsNotEmpty()
  @IsBoolean()
  required: boolean;

  @ApiProperty({
    description: 'Cấu hình chi tiết',
    example: {
      placeholder: 'Nhập màu sắc',
      maxLength: 50,
      description: 'Màu sắc chính của sản phẩm',
    },
  })
  @IsNotEmpty()
  @IsObject()
  configJson: any;
}
