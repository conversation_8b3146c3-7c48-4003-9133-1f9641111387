import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsObject, IsOptional, IsString } from 'class-validator';
import { EntityStatusEnum, PriceTypeEnum } from '@modules/business/enums';
import { ProductPriceDto } from './product-price.dto';
import { ProductImageDto } from './product-image.dto';

/**
 * DTO cho response trả về thông tin sản phẩm của người dùng
 */
export class UserProductResponseDto {
  @ApiProperty({
    description: 'ID của sản phẩm',
    example: 123,
  })
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'Tên sản phẩm',
    example: 'Sản phẩm A',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Giá sản phẩm',
    type: () => ProductPriceDto
  })
  @Type(() => ProductPriceDto)
  price: ProductPriceDto;

  @ApiProperty({
    description: 'Loại giá',
    enum: PriceTypeEnum,
    example: PriceTypeEnum.HAS_PRICE,
  })
  @IsEnum(PriceTypeEnum)
  typePrice: PriceTypeEnum;

  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Mô tả chi tiết về sản phẩm A',
    nullable: true,
  })
  @IsOptional()
  @IsString()
  description: string | null;

  @ApiProperty({
    description: 'Hình ảnh sản phẩm',
    nullable: true,
    type: () => [ProductImageDto]
  })
  @IsOptional()
  @IsArray()
  @Type(() => ProductImageDto)
  images: ProductImageDto[] | null;

  @ApiProperty({
    description: 'Tags sản phẩm',
    nullable: true,
    type: [String]
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags: string[] | null;

  @ApiProperty({
    description: 'ID người tạo',
    example: 456,
  })
  @IsNumber()
  createdBy: number;

  @ApiProperty({
    description: 'Thời gian tạo',
    example: 1625097600000,
  })
  @IsNumber()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật',
    example: 1625184000000,
  })
  @IsNumber()
  updatedAt: number;

  @ApiProperty({
    description: 'Cấu hình vận chuyển',
    example: {
      widthCm: 25,
      heightCm: 5,
      lengthCm: 30,
      weightGram: 200
    },
    nullable: true,
  })
  @IsOptional()
  @IsObject()
  shipmentConfig: Record<string, any> | null;

  @ApiProperty({
    description: 'Trạng thái của sản phẩm',
    enum: EntityStatusEnum,
    example: EntityStatusEnum.APPROVED,
    nullable: false,
  })
  @IsEnum(EntityStatusEnum)
  status: EntityStatusEnum;
}
