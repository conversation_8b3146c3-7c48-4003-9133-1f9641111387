import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsObject } from 'class-validator';

/**
 * DTO cho giá trị của trường tùy chỉnh
 */
export class CustomFieldValueDto {
  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> trị của trường',
    example: 'Đỏ',
  })
  value: string | number | boolean | string[] | number[] | null;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> thuộc tính tùy chỉnh khác',
    required: false
  })
  @IsOptional()
  @IsObject()
  additionalProperties?: Record<string, unknown>;
}
