import { ApiProperty } from '@nestjs/swagger';
import { WarehouseTypeEnum } from '@modules/business/enums';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsEnum, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { CustomFieldValueResponseDto } from './warehouse-custom-field-response.dto';

/**
 * DTO cho chi tiết kho vật lý
 */
export class PhysicalWarehouseDetailsDto {
  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Storage St, Warehouse City'
  })
  @IsString()
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Sức chứa kho',
    example: 5000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  capacity?: number;

  @ApiProperty({
    description: 'Diện tích kho (m2)',
    example: 1000,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  area?: number;

  @ApiProperty({
    description: 'Số lượng nhân viên',
    example: 10,
    required: false
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  employeeCount?: number;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<PhysicalWarehouseDetailsDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho chi tiết kho ảo
 */
export class VirtualWarehouseDetailsDto {
  @ApiProperty({
    description: 'URL của kho ảo',
    example: 'https://virtual-warehouse.example.com/main',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  url?: string;

  @ApiProperty({
    description: 'API key để truy cập kho ảo',
    example: 'vw_api_key_123',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  apiKey?: string;

  @ApiProperty({
    description: 'Hệ thống liên kết',
    example: 'ERP System',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  associatedSystem?: string;

  @ApiProperty({
    description: 'Mục đích sử dụng',
    example: 'Digital inventory management',
    required: false
  })
  @IsOptional()
  @IsString()
  @Expose()
  purpose?: string;

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<VirtualWarehouseDetailsDto>) {
    Object.assign(this, partial);
  }
}

/**
 * DTO cho response thông tin chi tiết kho
 */
export class WarehouseDetailResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 1
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Tên kho',
    example: 'Kho chính'
  })
  @IsString()
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Mô tả kho',
    example: 'Kho chứa hàng hóa chính của công ty',
    nullable: true
  })
  @IsOptional()
  @IsString()
  @Expose()
  description: string;

  @ApiProperty({
    description: 'Loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL
  })
  @IsEnum(WarehouseTypeEnum)
  @Expose()
  type: WarehouseTypeEnum;

  @ApiProperty({
    description: 'Chi tiết kho',
    type: Object,
    example: {
      // For PHYSICAL type
      address: '123 Storage St, Warehouse City',
      capacity: 5000
      // OR for VIRTUAL type
      // associatedSystem: 'ERP System',
      // purpose: 'Digital inventory management'
    }
  })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => Object)
  @Expose()
  details?: PhysicalWarehouseDetailsDto | VirtualWarehouseDetailsDto;

  /**
   * Helper method để xác định kiểu của details dựa trên loại kho
   * @returns Kiểu của details
   */
  getDetailsType(): typeof PhysicalWarehouseDetailsDto | typeof VirtualWarehouseDetailsDto {
    return this.type === WarehouseTypeEnum.PHYSICAL
      ? PhysicalWarehouseDetailsDto
      : VirtualWarehouseDetailsDto;
  }

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CustomFieldValueResponseDto],
    nullable: true
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldValueResponseDto)
  @Expose()
  customFields?: CustomFieldValueResponseDto[];

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<WarehouseDetailResponseDto>) {
    Object.assign(this, partial);
  }
}
