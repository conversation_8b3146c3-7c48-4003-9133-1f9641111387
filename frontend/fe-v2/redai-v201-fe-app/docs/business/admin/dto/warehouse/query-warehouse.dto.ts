import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * DTO cho truy vấn danh sách kho
 */
export class QueryWarehouseDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'Lọc theo loại kho',
    enum: WarehouseTypeEnum,
    example: WarehouseTypeEnum.PHYSICAL
  })
  @IsOptional()
  @IsEnum(WarehouseTypeEnum)
  type?: WarehouseTypeEnum;

  @ApiPropertyOptional({
    description: 'Tìm kiếm theo tên hoặc mô tả kho',
    example: 'Kho chính'
  })
  @IsOptional()
  @IsString()
  declare search?: string;
}
