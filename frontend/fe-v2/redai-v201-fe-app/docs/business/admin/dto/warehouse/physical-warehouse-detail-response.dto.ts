import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { IsArray, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { WarehouseResponseDto } from './warehouse-response.dto';
import { CustomFieldValueResponseDto } from './warehouse-custom-field-response.dto';

/**
 * DTO cho response thông tin chi tiết kho vật lý
 */
export class PhysicalWarehouseDetailResponseDto {
  @ApiProperty({
    description: 'ID của kho',
    example: 1
  })
  @IsNumber()
  @Expose()
  warehouseId: number;

  @ApiProperty({
    description: 'Địa chỉ kho',
    example: '123 Storage St, Warehouse City'
  })
  @IsString()
  @Expose()
  address: string;

  @ApiProperty({
    description: 'Sức chứa kho',
    example: 5000,
    nullable: true
  })
  @IsOptional()
  @IsNumber()
  @Expose()
  capacity: number;

  @ApiProperty({
    description: 'Thông tin kho',
    type: () => WarehouseResponseDto
  })
  @Type(() => WarehouseResponseDto)
  @IsObject()
  @ValidateNested()
  @Expose()
  warehouse: WarehouseResponseDto;

  @ApiProperty({
    description: 'Danh sách trường tùy chỉnh',
    type: [CustomFieldValueResponseDto],
    nullable: true
  })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CustomFieldValueResponseDto)
  @Expose()
  customFields?: CustomFieldValueResponseDto[];

  /**
   * Constructor
   * @param partial Dữ liệu một phần của DTO
   */
  constructor(partial: Partial<PhysicalWarehouseDetailResponseDto>) {
    Object.assign(this, partial);
  }
}
