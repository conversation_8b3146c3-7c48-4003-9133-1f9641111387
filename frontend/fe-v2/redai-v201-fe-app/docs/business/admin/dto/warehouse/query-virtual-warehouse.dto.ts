import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto/query.dto';

/**
 * DTO cho truy vấn danh sách kho ảo
 */
export class QueryVirtualWarehouseDto extends QueryDto {
  @ApiPropertyOptional({
    description: '<PERSON>ọ<PERSON> theo hệ thống liên kết',
    example: 'ERP'
  })
  @IsOptional()
  @IsString()
  associatedSystem?: string;
}
