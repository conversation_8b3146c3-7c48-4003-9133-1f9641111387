import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

/**
 * DTO cho response của folder
 */
export class FolderResponseDto {
  @ApiProperty({
    description: 'ID của thư mục',
    example: 1,
  })
  @Expose()
  id: number;

  @ApiProperty({
    description: 'Tên thư mục',
    example: 'Documents',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'ID thư mục cha',
    example: 1,
    nullable: true,
  })
  @Expose()
  parentId: number | null;

  @ApiProperty({
    description: 'ID người dùng sở hữu',
    example: 1,
  })
  @Expose()
  userId: number;

  @ApiPropertyOptional({
    description: 'Đường dẫn thư mục',
    example: '/Documents/Reports',
    nullable: true,
  })
  @Expose()
  path: string | null;

  @ApiPropertyOptional({
    description: 'ID kho ảo gốc',
    example: 1,
    nullable: true,
  })
  @Expose()
  root: number | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1620000000000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1620000000000,
  })
  @Expose()
  updatedAt: number;
}
