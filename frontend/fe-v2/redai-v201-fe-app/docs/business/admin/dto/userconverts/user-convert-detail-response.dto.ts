import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { UserConvertResponseDto } from './user-convert-response.dto';
import { UserConvertCustomerResponseDto } from './user-convert-customer-response.dto';

/**
 * DTO cho response trả về thông tin chi tiết bản ghi chuyển đổi khách hàng
 * bao gồm thông tin khách hàng được chuyển đổi
 */
export class UserConvertDetailResponseDto extends UserConvertResponseDto {
  @ApiProperty({
    description: 'Thông tin chi tiết khách hàng được chuyển đổi',
    type: UserConvertCustomerResponseDto,
    nullable: true,
    example: {
      id: 1,
      avatar: 'avatars/customer-123.jpg',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      email: { primary: '<EMAIL>' },
      phone: '0912345678',
      platform: 'Facebook',
      timezone: 'Asia/Ho_Chi_Minh',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      userId: 1,
      agentId: '550e8400-e29b-41d4-a716-446655440000',
      metadata: []
    }
  })
  @Type(() => UserConvertCustomerResponseDto)
  customer?: UserConvertCustomerResponseDto | null;
}
