import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { UserConvertCustomerResponseDto } from './user-convert-customer-response.dto';
import { UserConvertResponseDto } from './user-convert-response.dto';

/**
 * DTO cho response trả về chi tiết khách hàng chuyển đổi bao gồm lịch sử chuyển đổi
 */
export class UserConvertCustomerDetailResponseDto extends UserConvertCustomerResponseDto {
  @ApiProperty({
    description: 'Danh sách bản ghi chuyển đổi của khách hàng',
    type: [UserConvertResponseDto],
    example: [
      {
        id: 1,
        convertCustomerId: 1,
        userId: 1,
        conversionType: 'online',
        source: 'website',
        notes: '<PERSON>h<PERSON>ch hàng quan tâm đến sản phẩm X',
        content: { additionalInfo: 'Thông tin thêm về khách hàng' },
        createdAt: 1625097600000,
        updatedAt: 1625097600000
      }
    ],
    examples: [
      [
        {
          id: 1,
          convertCustomerId: 1,
          userId: 1,
          conversionType: 'online',
          source: 'website',
          notes: 'Khách hàng quan tâm đến sản phẩm X',
          content: { additionalInfo: 'Thông tin thêm về khách hàng' },
          createdAt: 1625097600000,
          updatedAt: 1625097600000
        }
      ],
      []
    ]
  })
  @IsArray({ message: 'Danh sách bản ghi chuyển đổi phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => UserConvertResponseDto)
  converts: UserConvertResponseDto[];
}
