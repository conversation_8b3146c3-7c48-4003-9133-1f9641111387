import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';
import { UserOrderResponseDto } from './user-order-response.dto';
import { UserConvertCustomerResponseDto } from './user-convert-customer-response.dto';

/**
 * DTO cho response trả về chi tiết đơn hàng bao gồm thông tin khách hàng
 */
export class UserOrderDetailResponseDto extends UserOrderResponseDto {
  @ApiProperty({
    description: 'Thông tin khách hàng đặt đơn',
    type: UserConvertCustomerResponseDto,
    example: {
      id: 1,
      name: '<PERSON>uyễn Văn A',
      phone: '0912345678',
      email: { primary: '<EMAIL>' },
      platform: 'Facebook',
      createdAt: 1625097600000,
      updatedAt: 1625097600000,
      metadata: [{ fieldName: 'address', fieldValue: '<PERSON><PERSON>' }]
    },
    examples: [
      {
        id: 1,
        name: '<PERSON><PERSON><PERSON><PERSON>',
        phone: '0912345678',
        email: { primary: '<EMAIL>' },
        platform: 'Facebook',
        createdAt: 1625097600000,
        updatedAt: 1625097600000,
        metadata: [{ fieldName: 'address', fieldValue: 'Hà Nội' }]
      },
      null
    ],
    nullable: true,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => UserConvertCustomerResponseDto)
  customer?: UserConvertCustomerResponseDto | null;
}
