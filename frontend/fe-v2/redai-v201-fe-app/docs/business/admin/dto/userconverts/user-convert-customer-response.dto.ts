import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsObject, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { EmailData, MetadataField } from '../interfaces';

/**
 * DTO cho response trả về thông tin khách hàng chuyển đổi
 */
export class UserConvertCustomerResponseDto {
  @ApiProperty({
    description: 'ID khách hàng',
    example: 1,
    examples: [1, 2, 3]
  })
  @IsNotEmpty({ message: 'ID khách hàng không được để trống' })
  @IsNumber({}, { message: 'ID khách hàng phải là số' })
  id: number;

  @ApiProperty({
    description: 'Ảnh đại diện',
    example: 'avatars/customer-123.jpg',
    examples: ['avatars/customer-123.jpg', 'avatars/profile.png', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Ảnh đại diện phải là chuỗi' })
  avatar: string | null;

  @ApiProperty({
    description: 'Tên khách hàng',
    example: 'Nguyễn Văn A',
    examples: ['Nguyễn Văn A', 'Trần Thị B', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Tên khách hàng phải là chuỗi' })
  name: string | null;

  @ApiProperty({
    description: 'Email khách hàng (dạng JSON)',
    example: { primary: '<EMAIL>', secondary: '<EMAIL>' },
    examples: [
      { primary: '<EMAIL>', secondary: '<EMAIL>' },
      { primary: '<EMAIL>' },
      null
    ],
    nullable: true,
    type: () => EmailData
  })
  @IsOptional()
  @IsObject({ message: 'Email khách hàng phải là đối tượng JSON' })
  @Type(() => EmailData)
  email: EmailData | null;

  @ApiProperty({
    description: 'Số điện thoại khách hàng',
    example: '0912345678',
    examples: ['0912345678', '0987654321', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại khách hàng phải là chuỗi' })
  phone: string | null;

  @ApiProperty({
    description: 'Nền tảng nguồn (Facebook, Web,...)',
    example: 'Facebook',
    examples: ['Facebook', 'Web', 'Instagram', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Nền tảng nguồn phải là chuỗi' })
  platform: string | null;

  @ApiProperty({
    description: 'Múi giờ của khách hàng',
    example: 'Asia/Ho_Chi_Minh',
    examples: ['Asia/Ho_Chi_Minh', 'UTC', null],
    nullable: true,
  })
  @IsOptional()
  @IsString({ message: 'Múi giờ của khách hàng phải là chuỗi' })
  timezone: string | null;

  @ApiProperty({
    description: 'Thời gian tạo (millis)',
    example: 1625097600000,
    examples: [1625097600000, 1630000000000]
  })
  @IsNotEmpty({ message: 'Thời gian tạo không được để trống' })
  @IsNumber({}, { message: 'Thời gian tạo phải là số' })
  createdAt: number;

  @ApiProperty({
    description: 'Thời gian cập nhật (millis)',
    example: 1625184000000,
    examples: [1625184000000, 1640000000000]
  })
  @IsNotEmpty({ message: 'Thời gian cập nhật không được để trống' })
  @IsNumber({}, { message: 'Thời gian cập nhật phải là số' })
  updatedAt: number;

  @ApiProperty({
    description: 'ID người dùng sở hữu khách hàng',
    example: 1,
    examples: [1, 2, 3, null],
    nullable: true,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID người dùng phải là số' })
  userId: number | null;

  @ApiProperty({
    description: 'ID agent hỗ trợ khách hàng',
    example: '550e8400-e29b-41d4-a716-************',
    examples: ['550e8400-e29b-41d4-a716-************', '123e4567-e89b-12d3-a456-************', null],
    nullable: true,
  })
  @IsOptional()
  @IsUUID('4', { message: 'ID agent phải là UUID phiên bản 4 hợp lệ' })
  agentId: string | null;

  @ApiProperty({
    description: 'Trường tùy chỉnh',
    example: [{ fieldName: 'address', fieldValue: 'Hà Nội' }],
    examples: [
      [{ fieldName: 'address', fieldValue: 'Hà Nội' }],
      [{ fieldName: 'job', fieldValue: 'Developer' }, { fieldName: 'age', fieldValue: 30 }],
      []
    ],
    type: () => [MetadataField]
  })
  @IsArray({ message: 'Trường tùy chỉnh phải là mảng' })
  @ValidateNested({ each: true })
  @Type(() => MetadataField)
  metadata: MetadataField[];
}
