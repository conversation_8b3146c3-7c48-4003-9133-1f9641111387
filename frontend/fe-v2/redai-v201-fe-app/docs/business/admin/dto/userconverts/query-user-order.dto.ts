import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto } from '@dto/query.dto';

/**
 * DTO cho các tham số truy vấn danh sách đơn hàng của người dùng
 */
export class QueryUserOrderDto extends QueryDto {
  @ApiPropertyOptional({
    description: 'ID người dùng sở hữu đơn hàng',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userId?: number;

  @ApiPropertyOptional({
    description: 'ID khách hàng đặt đơn',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  userConvertCustomerId?: number;

  @ApiPropertyOptional({
    description: 'Đơn hàng có yêu cầu vận chuyển hay không',
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  hasShipping?: boolean;

  @ApiPropertyOptional({
    description: 'Trạng thái vận chuyển (ví dụ: pending, shipped, delivered)',
    example: 'pending'
  })
  @IsOptional()
  @IsString()
  shippingStatus?: string;

  @ApiPropertyOptional({
    description: 'Nguồn đơn hàng',
    example: 'website'
  })
  @IsOptional()
  @IsString()
  source?: string;

  @ApiPropertyOptional({
    description: 'Thời gian tạo từ (timestamp)',
    example: 1625097600000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtFrom?: number;

  @ApiPropertyOptional({
    description: 'Thời gian tạo đến (timestamp)',
    example: 1625184000000
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  createdAtTo?: number;
}
