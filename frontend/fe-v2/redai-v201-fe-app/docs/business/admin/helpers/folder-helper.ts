import { Injectable } from '@nestjs/common';
import { Folder } from '@modules/business/entities/folder.entity';

/**
 * Helper xử lý các thao tác liên quan đến folder
 */
@Injectable()
export class FolderHelper {
  /**
   * Định dạng thời gian Unix timestamp thành chuỗi ngày giờ
   * @param timestamp Thời gian Unix timestamp (millis)
   * @returns Chuỗi ngày giờ đã định dạng
   */
  formatTimestamp(timestamp: number): string {
    const date = new Date(timestamp);
    return date.toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  }

  /**
   * Tạo đường dẫn thư mục từ tên thư mục và đường dẫn thư mục cha
   * @param folderName Tên thư mục
   * @param parentPath Đường dẫn thư mục cha
   * @returns Đường dẫn thư mục
   */
  createFolderPath(folderName: string, parentPath?: string): string {
    if (!parentPath) {
      return `/${folderName}`;
    }
    return `${parentPath}/${folderName}`;
  }

  /**
   * Lấy tên thư mục từ đường dẫn
   * @param path Đường dẫn thư mục
   * @returns Tên thư mục
   */
  getFolderNameFromPath(path: string): string {
    if (!path) return '';
    const parts = path.split('/').filter(part => part);
    return parts.length > 0 ? parts[parts.length - 1] : '';
  }

  /**
   * Lấy đường dẫn thư mục cha từ đường dẫn
   * @param path Đường dẫn thư mục
   * @returns Đường dẫn thư mục cha
   */
  getParentPathFromPath(path: string): string | null {
    if (!path) return null;
    const parts = path.split('/').filter(part => part);
    if (parts.length <= 1) return null;
    return '/' + parts.slice(0, parts.length - 1).join('/');
  }
}
