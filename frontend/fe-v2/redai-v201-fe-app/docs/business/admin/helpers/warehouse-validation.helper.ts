import { Injectable } from '@nestjs/common';
import { AppException } from '@common/exceptions';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import { Warehouse, PhysicalWarehouse, VirtualWarehouse, WarehouseCustomField } from '@modules/business/entities';
import { WarehouseTypeEnum } from '@modules/business/enums';

/**
 * Helper xử lý validation cho kho
 */
@Injectable()
export class WarehouseValidationHelper {
  /**
   * Kiểm tra kho có tồn tại không
   * @param warehouse Kho cần kiểm tra
   * @throws AppException nếu kho không tồn tại
   */
  validateWarehouseExists(warehouse: Warehouse | null): void {
    if (!warehouse) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND,
        'Kho không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra loại kho có hợp lệ không
   * @param warehouse Kho cần kiểm tra
   * @param expectedType Loại kho mong đợi
   * @throws AppException nếu loại kho không hợp lệ
   */
  validateWarehouseType(warehouse: Warehouse | null, expectedType: WarehouseTypeEnum): void {
    // Kiểm tra kho có tồn tại không trước khi kiểm tra loại kho
    this.validateWarehouseExists(warehouse);
    // Sau khi validateWarehouseExists, warehouse không thể là null
    // Nếu warehouse là null, validateWarehouseExists sẽ throw exception
    if (warehouse!.type !== expectedType) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE,
        `Kho này không phải là loại ${expectedType}`
      );
    }
  }

  /**
   * Kiểm tra kho vật lý có tồn tại không
   * @param physicalWarehouse Kho vật lý cần kiểm tra
   * @throws AppException nếu kho vật lý không tồn tại
   */
  validatePhysicalWarehouseExists(physicalWarehouse: PhysicalWarehouse | null): void {
    if (!physicalWarehouse) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND,
        'Kho vật lý không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra kho ảo có tồn tại không
   * @param virtualWarehouse Kho ảo cần kiểm tra
   * @throws AppException nếu kho ảo không tồn tại
   */
  validateVirtualWarehouseExists(virtualWarehouse: VirtualWarehouse | null): void {
    if (!virtualWarehouse) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND,
        'Kho ảo không tồn tại'
      );
    }
  }

  /**
   * Kiểm tra trường tùy chỉnh của kho có tồn tại không
   * @param customField Trường tùy chỉnh cần kiểm tra
   * @throws AppException nếu trường tùy chỉnh không tồn tại
   */
  validateWarehouseCustomFieldExists(customField: WarehouseCustomField | null): void {
    if (!customField) {
      throw new AppException(
        ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
        'Trường tùy chỉnh của kho không tồn tại'
      );
    }
  }
}
