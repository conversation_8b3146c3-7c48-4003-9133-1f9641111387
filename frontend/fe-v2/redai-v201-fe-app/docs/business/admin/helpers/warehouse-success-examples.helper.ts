/**
 * Helper chứa các ví dụ cụ thể cho API success response của các controller warehouse
 */
export const WAREHOUSE_SUCCESS_EXAMPLES = {
  // Warehouse success examples
  WAREHOUSE_LIST: {
    code: 0,
    message: 'L<PERSON>y danh sách kho thành công',
    result: {
      items: [
        {
          warehouseId: 1,
          name: '<PERSON>ho chính',
          description: '<PERSON>ho chứa hàng hóa chính của công ty',
          type: 'PHYSICAL'
        },
        {
          warehouseId: 2,
          name: '<PERSON>ho phụ',
          description: '<PERSON>ho chứa hàng hóa phụ của công ty',
          type: 'VIRTUAL'
        }
      ],
      meta: {
        totalItems: 2,
        itemCount: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    }
  },
  
  WAREHOUSE_DETAIL: {
    code: 0,
    message: 'Lấy thông tin chi tiết kho thành công',
    result: {
      warehouseId: 1,
      name: '<PERSON><PERSON> ch<PERSON><PERSON>',
      description: '<PERSON>ho chứa hàng hóa chính của công ty',
      type: 'PHYSICAL',
      customFields: [
        {
          fieldId: 3,
          label: 'Khu vực',
          type: 'TEXT',
          value: {
            value: 'North'
          }
        }
      ]
    }
  },
  
  // Physical warehouse success examples
  PHYSICAL_WAREHOUSE_LIST: {
    code: 0,
    message: 'Lấy danh sách kho vật lý thành công',
    result: {
      items: [
        {
          warehouseId: 1,
          address: '123 Storage St, Warehouse City',
          capacity: 5000,
          warehouse: {
            warehouseId: 1,
            name: 'Kho chính',
            description: 'Kho chứa hàng hóa chính của công ty',
            type: 'PHYSICAL'
          }
        },
        {
          warehouseId: 3,
          address: '456 Logistics Ave, Storage Town',
          capacity: 3000,
          warehouse: {
            warehouseId: 3,
            name: 'Kho phụ',
            description: 'Kho chứa hàng hóa phụ của công ty',
            type: 'PHYSICAL'
          }
        }
      ],
      meta: {
        totalItems: 2,
        itemCount: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    }
  },
  
  PHYSICAL_WAREHOUSE_DETAIL: {
    code: 0,
    message: 'Lấy thông tin chi tiết kho vật lý thành công',
    result: {
      warehouseId: 1,
      address: '123 Storage St, Warehouse City',
      capacity: 5000,
      warehouse: {
        warehouseId: 1,
        name: 'Kho chính',
        description: 'Kho chứa hàng hóa chính của công ty',
        type: 'PHYSICAL'
      },
      customFields: [
        {
          fieldId: 3,
          label: 'Khu vực',
          type: 'TEXT',
          value: {
            value: 'North'
          }
        }
      ]
    }
  },
  
  // Virtual warehouse success examples
  VIRTUAL_WAREHOUSE_LIST: {
    code: 0,
    message: 'Lấy danh sách kho ảo thành công',
    result: {
      items: [
        {
          warehouseId: 2,
          url: 'https://virtual-warehouse.example.com/main',
          apiKey: 'vw_api_key_123',
          warehouse: {
            warehouseId: 2,
            name: 'Kho ảo chính',
            description: 'Kho ảo chính của công ty',
            type: 'VIRTUAL'
          }
        },
        {
          warehouseId: 4,
          url: 'https://virtual-warehouse.example.com/secondary',
          apiKey: 'vw_api_key_456',
          warehouse: {
            warehouseId: 4,
            name: 'Kho ảo phụ',
            description: 'Kho ảo phụ của công ty',
            type: 'VIRTUAL'
          }
        }
      ],
      meta: {
        totalItems: 2,
        itemCount: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    }
  },
  
  VIRTUAL_WAREHOUSE_DETAIL: {
    code: 0,
    message: 'Lấy thông tin chi tiết kho ảo thành công',
    result: {
      warehouseId: 2,
      url: 'https://virtual-warehouse.example.com/main',
      apiKey: 'vw_api_key_123',
      warehouse: {
        warehouseId: 2,
        name: 'Kho ảo chính',
        description: 'Kho ảo chính của công ty',
        type: 'VIRTUAL'
      },
      customFields: [
        {
          fieldId: 5,
          label: 'Nhà cung cấp',
          type: 'TEXT',
          value: {
            value: 'VirtualStorage Inc.'
          }
        }
      ]
    }
  },
  
  // Warehouse custom field success examples
  WAREHOUSE_CUSTOM_FIELD_LIST: {
    code: 0,
    message: 'Lấy danh sách trường tùy chỉnh của kho thành công',
    result: {
      items: [
        {
          warehouseId: 1,
          fieldId: 3,
          value: { value: 'North' },
          warehouseName: 'Kho chính',
          fieldLabel: 'Khu vực'
        },
        {
          warehouseId: 2,
          fieldId: 5,
          value: { value: 'VirtualStorage Inc.' },
          warehouseName: 'Kho ảo chính',
          fieldLabel: 'Nhà cung cấp'
        }
      ],
      meta: {
        totalItems: 2,
        itemCount: 2,
        itemsPerPage: 10,
        totalPages: 1,
        currentPage: 1
      }
    }
  },
  
  WAREHOUSE_CUSTOM_FIELD_DETAIL: {
    code: 0,
    message: 'Lấy thông tin chi tiết trường tùy chỉnh của kho thành công',
    result: {
      warehouseId: 1,
      fieldId: 3,
      value: { value: 'North' },
      warehouseName: 'Kho chính',
      fieldLabel: 'Khu vực',
      fieldDetails: {
        label: 'Khu vực',
        type: 'TEXT',
        required: true,
        configJson: { options: ['North', 'South', 'East', 'West'] }
      }
    }
  }
};
