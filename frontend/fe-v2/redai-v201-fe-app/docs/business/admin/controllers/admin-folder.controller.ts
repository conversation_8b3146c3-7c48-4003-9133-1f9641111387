import { Controller, Get, Param, ParseIntPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { AdminFolderService } from '../services/admin-folder.service';
import { FolderQueryDto, FolderResponseDto, FolderDetailResponseDto, ParentFolderInfoDto, OwnerInfoDto } from '../dto/folder';
import { FileResponseDto } from '../dto/file';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { ApiErrorResponse } from '@common/error/api-error-response.decorator';
import { FOLDER_ERROR_CODES } from '../exceptions/folder.exception';

/**
 * Controller xử lý các API liên quan đến folder cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_FOLDER)
@ApiExtraModels(ApiResponseDto, FolderResponseDto, FolderDetailResponseDto, ParentFolderInfoDto, OwnerInfoDto, FileResponseDto, PaginatedResult)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/folders')
export class AdminFolderController {
  constructor(private readonly adminFolderService: AdminFolderService) {}

  /**
   * Lấy danh sách folder với phân trang và tìm kiếm
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách folder với phân trang và tìm kiếm' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách folder',
    schema: ApiResponseDto.getPaginatedSchema(FolderResponseDto),
  })
  @ApiErrorResponse(
    FOLDER_ERROR_CODES.FOLDER_FIND_FAILED,
  )
  async findAll(
    @Query() queryDto: FolderQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<FolderResponseDto>>> {
    const result = await this.adminFolderService.findAll(queryDto);
    return ApiResponseDto.paginated(result);
  }

  /**
   * Lấy chi tiết folder theo ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Lấy chi tiết folder theo ID' })
  @ApiParam({ name: 'id', description: 'ID của folder', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Chi tiết folder',
    schema: ApiResponseDto.getSchema(FolderDetailResponseDto),
  })
  @ApiErrorResponse(
    FOLDER_ERROR_CODES.FOLDER_NOT_FOUND,
    FOLDER_ERROR_CODES.FOLDER_DETAIL_FAILED,
  )
  async findById(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ApiResponseDto<FolderDetailResponseDto>> {
    const result = await this.adminFolderService.findById(id);
    return ApiResponseDto.success(result);
  }
}
