import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  Post,
  Body,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ProductAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { QueryUserProductDto } from '@modules/business/admin/dto';
import { UserProductResponseDto } from '@modules/business/admin/dto';
import { UserProductDetailResponseDto } from '@modules/business/admin/dto';
import { UpdateProductStatusDto } from '../dto/customfields/update-product-status.dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến sản phẩm của người dùng cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  UserProductResponseDto,
  UserProductDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/user-products')
export class ProductAdminController {
  constructor(private readonly productAdminService: ProductAdminService) {}

  /**
   * Lấy danh sách tất cả sản phẩm của người dùng với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách sản phẩm phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách tất cả sản phẩm của người dùng',
    description: 'Lấy danh sách tất cả sản phẩm của người dùng với phân trang, tìm kiếm, lọc và sắp xếp. Có thể lọc theo userId để lấy sản phẩm của một người dùng cụ thể. Có thể lọc theo status (PENDING, APPROVED, REJECTED) để lấy sản phẩm theo trạng thái.',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách sản phẩm',
    schema: ApiResponseDto.getPaginatedSchema(UserProductResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.PRODUCT_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getAllProducts(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryUserProductDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserProductResponseDto>>> {
    const result = await this.productAdminService.getProducts(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách sản phẩm thành công');
  }

  /**
   * Lấy thông tin chi tiết sản phẩm theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param productId ID của sản phẩm
   * @returns Thông tin chi tiết sản phẩm
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết sản phẩm theo ID',
    description: 'Lấy thông tin chi tiết của một sản phẩm cụ thể bao gồm thông tin trường tùy chỉnh',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của sản phẩm',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết sản phẩm',
    schema: ApiResponseDto.getSchema(UserProductDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_PRODUCT_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.PRODUCT_DETAIL_FETCH_ERROR,
  )
  async getProductById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) productId: number,
  ): Promise<ApiResponseDto<UserProductDetailResponseDto>> {
    const result = await this.productAdminService.getProductById(employeeId, productId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết sản phẩm thành công');
  }

  /**
   * Cập nhật trạng thái sản phẩm
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param updateStatusDto DTO chứa thông tin cập nhật trạng thái
   * @returns Thông báo kết quả cập nhật
   */
  @Post('status')
  @ApiOperation({
    summary: 'Cập nhật trạng thái sản phẩm, kiểm duyệt sản phẩm',
    description: 'Cập nhật trạng thái cho một hoặc nhiều sản phẩm. Chỉ có thể chuyển từ PENDING sang APPROVED hoặc REJECTED. Cần cung cấp lý do từ chối khi chuyển sang REJECTED.',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật trạng thái thành công',
    schema: ApiResponseDto.getSchema({ updatedCount: { type: 'number', example: 2 } }),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.PRODUCT_STATUS_UPDATE_ERROR,
    BUSINESS_ADMIN_ERROR_CODES.PRODUCT_STATUS_INVALID_TRANSITION,
    BUSINESS_ADMIN_ERROR_CODES.PRODUCT_REJECT_REASON_REQUIRED,
    BUSINESS_ADMIN_ERROR_CODES.USER_PRODUCT_NOT_FOUND,
  )
  async updateProductStatus(
    @CurrentEmployee('id') employeeId: number,
    @Body() updateStatusDto: UpdateProductStatusDto,
  ): Promise<ApiResponseDto<{ updatedCount: number }>> {
    const updatedCount = await this.productAdminService.updateProductStatus(employeeId, updateStatusDto);
    return ApiResponseDto.success({ updatedCount }, 'Cập nhật trạng thái sản phẩm thành công');
  }
}
