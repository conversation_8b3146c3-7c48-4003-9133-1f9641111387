import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import { AdminWarehouseCustomFieldService } from '@modules/business/admin/services';
import {
  QueryWarehouseCustomFieldDto,
  WarehouseCustomFieldResponseDto,
  WarehouseCustomFieldDetailResponseDto
} from '../dto/warehouse';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý API liên quan đến trường tùy chỉnh của kho cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_WAREHOUSE)
@ApiExtraModels(
  ApiResponseDto,
  WarehouseCustomFieldResponseDto,
  WarehouseCustomFieldDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/warehouse-custom-fields')
export class AdminWarehouseCustomFieldController {
  constructor(private readonly adminWarehouseCustomFieldService: AdminWarehouseCustomFieldService) {}

  /**
   * Lấy danh sách trường tùy chỉnh của kho với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách trường tùy chỉnh của kho với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh của kho',
    schema: ApiResponseDto.getPaginatedSchema(WarehouseCustomFieldResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED
  )
  async findAll(
    @Query() queryDto: QueryWarehouseCustomFieldDto
  ): Promise<ApiResponseDto<PaginatedResult<WarehouseCustomFieldResponseDto>>> {
    const result = await this.adminWarehouseCustomFieldService.findAll(queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách trường tùy chỉnh của kho thành công');
  }

  /**
   * Lấy thông tin chi tiết trường tùy chỉnh của kho theo ID kho và ID trường
   */
  @Get('warehouses/:warehouseId/fields/:fieldId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết trường tùy chỉnh của kho theo ID kho và ID trường' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', example: 1 })
  @ApiParam({ name: 'fieldId', description: 'ID của trường tùy chỉnh', example: 3 })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết trường tùy chỉnh của kho',
    schema: ApiResponseDto.getSchema(WarehouseCustomFieldDetailResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_CUSTOM_FIELD_FIND_FAILED
  )
  async findOne(
    @Param('warehouseId') warehouseId: number,
    @Param('fieldId') fieldId: number
  ): Promise<ApiResponseDto<WarehouseCustomFieldDetailResponseDto>> {
    const result = await this.adminWarehouseCustomFieldService.findOne(warehouseId, fieldId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết trường tùy chỉnh của kho thành công');
  }
}
