import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import { AdminVirtualWarehouseService } from '@modules/business/admin/services';
import {
  QueryVirtualWarehouseDto,
  VirtualWarehouseResponseDto,
  VirtualWarehouseDetailResponseDto
} from '../dto/warehouse';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý API liên quan đến kho ảo cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_WAREHOUSE)
@ApiExtraModels(
  ApiResponseDto,
  VirtualWarehouseResponseDto,
  VirtualWarehouseDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/virtual-warehouses')
export class AdminVirtualWarehouseController {
  constructor(private readonly adminVirtualWarehouseService: AdminVirtualWarehouseService) {}

  /**
   * Lấy danh sách kho ảo với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho ảo với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho ảo',
    schema: ApiResponseDto.getPaginatedSchema(VirtualWarehouseResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED
  )
  async findAll(
    @Query() queryDto: QueryVirtualWarehouseDto
  ): Promise<ApiResponseDto<PaginatedResult<VirtualWarehouseResponseDto>>> {
    const result = await this.adminVirtualWarehouseService.findAll(queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách kho ảo thành công');
  }

  /**
   * Lấy thông tin chi tiết kho ảo theo ID
   */
  @Get(':warehouseId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết kho ảo theo ID' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', example: 2 })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết kho ảo',
    schema: ApiResponseDto.getSchema(VirtualWarehouseDetailResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE,
    ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.VIRTUAL_WAREHOUSE_FIND_FAILED
  )
  async findOne(
    @Param('warehouseId') warehouseId: number
  ): Promise<ApiResponseDto<VirtualWarehouseDetailResponseDto>> {
    const result = await this.adminVirtualWarehouseService.findOne(warehouseId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết kho ảo thành công');
  }
}
