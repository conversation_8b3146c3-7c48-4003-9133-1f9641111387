import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { UserConvertCustomerAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryUserConvertCustomerDto,
  UserConvertCustomerResponseDto,
  UserConvertCustomerDetailResponseDto
} from '../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến khách hàng chuyển đổi cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  UserConvertCustomerResponseDto,
  UserConvertCustomerDetailResponseDto,
  QueryUserConvertCustomerDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/user-convert-customers')
export class UserConvertCustomerAdminController {
  constructor(private readonly userConvertCustomerAdminService: UserConvertCustomerAdminService) {}

  /**
   * Lấy danh sách khách hàng chuyển đổi với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách khách hàng chuyển đổi phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách khách hàng chuyển đổi',
    description: 'Lấy danh sách khách hàng chuyển đổi với phân trang, tìm kiếm, lọc và sắp xếp',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách khách hàng chuyển đổi',
    schema: ApiResponseDto.getPaginatedSchema(UserConvertCustomerResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getAllUserConvertCustomers(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryUserConvertCustomerDto,
  ): Promise<ApiResponseDto<PaginatedResult<UserConvertCustomerResponseDto>>> {
    const result = await this.userConvertCustomerAdminService.getUserConvertCustomers(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách khách hàng chuyển đổi thành công');
  }

  /**
   * Lấy thông tin chi tiết khách hàng chuyển đổi theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param customerId ID của khách hàng chuyển đổi
   * @returns Thông tin chi tiết khách hàng chuyển đổi
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết khách hàng chuyển đổi theo ID',
    description: 'Lấy thông tin chi tiết của một khách hàng chuyển đổi cụ thể bao gồm lịch sử chuyển đổi',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của khách hàng chuyển đổi',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết khách hàng chuyển đổi',
    schema: ApiResponseDto.getSchema(UserConvertCustomerDetailResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.USER_CONVERT_CUSTOMER_FETCH_ERROR,
  )
  async getUserConvertCustomerById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) customerId: number,
  ): Promise<ApiResponseDto<UserConvertCustomerDetailResponseDto>> {
    const result = await this.userConvertCustomerAdminService.getUserConvertCustomerById(employeeId, customerId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết khách hàng chuyển đổi thành công');
  }
}
