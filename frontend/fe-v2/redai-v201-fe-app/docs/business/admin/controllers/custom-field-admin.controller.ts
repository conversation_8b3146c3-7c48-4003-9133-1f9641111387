import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  ParseIntPipe,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CustomFieldAdminService } from '@modules/business/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { Roles } from '@modules/auth/decorators/roles.decorator';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import {
  QueryCustomFieldDto,
  CreateCustomFieldDto,
  UpdateCustomFieldDto,
  CustomFieldResponseDto
} from '../dto';
import { ApiErrorResponse, ApiMultipleErrorResponses } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { BUSINESS_ADMIN_ERROR_CODES } from '@modules/business/admin/exceptions';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý các API liên quan đến trường tùy chỉnh cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS)
@ApiExtraModels(
  ApiResponseDto,
  CustomFieldResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/custom-fields')
export class CustomFieldAdminController {
  constructor(private readonly customFieldAdminService: CustomFieldAdminService) {}

  /**
   * Lấy danh sách trường tùy chỉnh với phân trang, tìm kiếm, lọc và sắp xếp
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách trường tùy chỉnh phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách trường tùy chỉnh',
    description: 'Lấy danh sách trường tùy chỉnh với phân trang, tìm kiếm, lọc và sắp xếp',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách trường tùy chỉnh',
    schema: ApiResponseDto.getPaginatedSchema(CustomFieldResponseDto),
  })
  @ApiMultipleErrorResponses(
    HttpStatus.INTERNAL_SERVER_ERROR,
    [
      BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_FETCH_ERROR,
      BUSINESS_ADMIN_ERROR_CODES.GENERAL_ERROR
    ]
  )
  async getAllCustomFields(
    @CurrentEmployee('id') employeeId: number,
    @Query() queryDto: QueryCustomFieldDto,
  ): Promise<ApiResponseDto<PaginatedResult<CustomFieldResponseDto>>> {
    const result = await this.customFieldAdminService.getCustomFields(employeeId, queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách trường tùy chỉnh thành công');
  }

  /**
   * Tạo trường tùy chỉnh mới
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param createDto DTO chứa thông tin trường tùy chỉnh mới
   * @returns Trường tùy chỉnh đã tạo
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo trường tùy chỉnh mới',
    description: 'Tạo trường tùy chỉnh mới với thông tin từ request body',
  })
  @ApiResponse({
    status: 201,
    description: 'Trường tùy chỉnh đã được tạo thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_CREATION_ERROR,
    BUSINESS_ADMIN_ERROR_CODES.CONFIG_ID_DUPLICATE,
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_VALIDATION_ERROR,
  )
  async createCustomField(
    @CurrentEmployee('id') employeeId: number,
    @Body() createDto: CreateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldAdminService.createCustomField(employeeId, createDto);
    return ApiResponseDto.created(result, 'Tạo trường tùy chỉnh thành công');
  }

  /**
   * Lấy thông tin chi tiết trường tùy chỉnh theo ID
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param fieldId ID của trường tùy chỉnh
   * @returns Thông tin chi tiết trường tùy chỉnh
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy thông tin chi tiết trường tùy chỉnh theo ID',
    description: 'Lấy thông tin chi tiết của một trường tùy chỉnh cụ thể',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết trường tùy chỉnh',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_FETCH_ERROR,
  )
  async getCustomFieldById(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) fieldId: number,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldAdminService.getCustomFieldById(employeeId, fieldId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết trường tùy chỉnh thành công');
  }

  /**
   * Cập nhật trường tùy chỉnh
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param fieldId ID của trường tùy chỉnh
   * @param updateDto DTO chứa thông tin cập nhật
   * @returns Trường tùy chỉnh đã cập nhật
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật trường tùy chỉnh',
    description: 'Cập nhật thông tin trường tùy chỉnh với ID cụ thể',
  })
  @ApiParam({
    name: 'id',
    required: true,
    type: Number,
    description: 'ID của trường tùy chỉnh',
    example: 1,
  })
  @ApiResponse({
    status: 200,
    description: 'Trường tùy chỉnh đã được cập nhật thành công',
    schema: ApiResponseDto.getSchema(CustomFieldResponseDto),
  })
  @ApiErrorResponse(
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_NOT_FOUND,
    BUSINESS_ADMIN_ERROR_CODES.CUSTOM_FIELD_UPDATE_ERROR,
    BUSINESS_ADMIN_ERROR_CODES.CONFIG_ID_DUPLICATE,
  )
  async updateCustomField(
    @CurrentEmployee('id') employeeId: number,
    @Param('id', ParseIntPipe) fieldId: number,
    @Body() updateDto: UpdateCustomFieldDto,
  ): Promise<ApiResponseDto<CustomFieldResponseDto>> {
    const result = await this.customFieldAdminService.updateCustomField(employeeId, fieldId, updateDto);
    return ApiResponseDto.success(result, 'Cập nhật trường tùy chỉnh thành công');
  }
}
