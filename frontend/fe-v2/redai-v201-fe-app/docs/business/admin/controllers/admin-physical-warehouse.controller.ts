import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { ApiResponseDto, PaginatedResult } from '@common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { ADMIN_WAREHOUSE_ERROR_CODES } from '../exceptions/warehouse.exception';
import { AdminPhysicalWarehouseService } from '@modules/business/admin/services';
import {
  QueryPhysicalWarehouseDto,
  PhysicalWarehouseResponseDto,
  PhysicalWarehouseDetailResponseDto
} from '../dto/warehouse';
import { SWAGGER_API_TAGS } from '@common/swagger';

/**
 * Controller xử lý API liên quan đến kho vật lý cho admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_BUSINESS_WAREHOUSE)
@ApiExtraModels(
  ApiResponseDto,
  PhysicalWarehouseResponseDto,
  PhysicalWarehouseDetailResponseDto,
  PaginatedResult,
  ApiErrorResponseDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/physical-warehouses')
export class AdminPhysicalWarehouseController {
  constructor(private readonly adminPhysicalWarehouseService: AdminPhysicalWarehouseService) {}

  /**
   * Lấy danh sách kho vật lý với phân trang
   */
  @Get()
  @ApiOperation({ summary: 'Lấy danh sách kho vật lý với phân trang' })
  @ApiResponse({
    status: 200,
    description: 'Danh sách kho vật lý',
    schema: ApiResponseDto.getPaginatedSchema(PhysicalWarehouseResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED
  )
  async findAll(
    @Query() queryDto: QueryPhysicalWarehouseDto
  ): Promise<ApiResponseDto<PaginatedResult<PhysicalWarehouseResponseDto>>> {
    const result = await this.adminPhysicalWarehouseService.findAll(queryDto);
    return ApiResponseDto.paginated(result, 'Lấy danh sách kho vật lý thành công');
  }

  /**
   * Lấy thông tin chi tiết kho vật lý theo ID
   */
  @Get(':warehouseId')
  @ApiOperation({ summary: 'Lấy thông tin chi tiết kho vật lý theo ID' })
  @ApiParam({ name: 'warehouseId', description: 'ID của kho', example: 1 })
  @ApiResponse({
    status: 200,
    description: 'Thông tin chi tiết kho vật lý',
    schema: ApiResponseDto.getSchema(PhysicalWarehouseDetailResponseDto)
  })
  @ApiErrorResponse(
    ADMIN_WAREHOUSE_ERROR_CODES.WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.INVALID_WAREHOUSE_TYPE,
    ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_NOT_FOUND,
    ADMIN_WAREHOUSE_ERROR_CODES.PHYSICAL_WAREHOUSE_FIND_FAILED
  )
  async findOne(
    @Param('warehouseId') warehouseId: number
  ): Promise<ApiResponseDto<PhysicalWarehouseDetailResponseDto>> {
    const result = await this.adminPhysicalWarehouseService.findOne(warehouseId);
    return ApiResponseDto.success(result, 'Lấy thông tin chi tiết kho vật lý thành công');
  }
}
