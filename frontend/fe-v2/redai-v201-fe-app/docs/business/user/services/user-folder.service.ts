import { Injectable, Logger } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';
import { PaginatedResult } from '@common/response';
import { Transactional } from 'typeorm-transactional';
import { Folder } from '@modules/business/entities';
import { FolderRepository, FileRepository } from '@modules/business/repositories';
import { ValidationHelper } from '../helpers/validation.helper';
import { JwtPayload } from '@modules/auth/guards/jwt.util';
import {
  CreateFolderDto,
  UpdateFolderDto,
  FolderResponseDto,
  FolderDetailResponseDto,
  QueryFolderDto,
} from '../dto/folder';

/**
 * Service xử lý nghiệp vụ liên quan đến thư mục
 */
@Injectable()
export class UserFolderService {
  private readonly logger = new Logger(UserFolderService.name);

  constructor(
    private readonly folderRepository: FolderRepository,
    private readonly fileRepository: FileRepository,
    private readonly validationHelper: ValidationHelper,
  ) {}

  /**
   * Tạo mới thư mục
   * @param createDto DTO chứa thông tin tạo thư mục mới
   * @param user Thông tin người dùng đã xác thực
   * @returns Thông tin thư mục đã tạo
   */
  @Transactional()
  async createFolder(createDto: CreateFolderDto, user: JwtPayload): Promise<FolderResponseDto> {
    const userId = user.id;
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateCreateFolder(createDto, userId);

      // Tạo thư mục mới
      const folder = new Folder();
      folder.name = createDto.name;

      // Xử lý parentId có thể null
      if (createDto.parentId === undefined || createDto.parentId === null) {
        (folder as any).parentId = null;
      } else {
        folder.parentId = createDto.parentId;
      }

      folder.userId = userId;

      // Xử lý root có thể null
      if (createDto.root === undefined || createDto.root === null) {
        (folder as any).root = null;
      } else {
        folder.root = createDto.root;
      }

      // Lưu ý: Không cần thiết lập warehouse_id vì đã có ràng buộc khóa ngoại tự động

      // Tạo đường dẫn thư mục
      if (createDto.parentId) {
        const parentFolder = await this.validationHelper.validateFolderExists(createDto.parentId);
        folder.path = parentFolder.path
          ? `${parentFolder.path}/${createDto.name}`
          : `/${createDto.name}`;
      } else {
        folder.path = `/${createDto.name}`;
      }

      // Lưu thư mục vào cơ sở dữ liệu
      const savedFolder = await this.folderRepository.createFolder(folder);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(FolderResponseDto, savedFolder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi tạo thư mục: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi ràng buộc khóa ngoại cụ thể
      if (error.message && error.message.includes('folders_virtual_warehouse_warehouse_id_fk')) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
          `Lỗi khi tạo thư mục: Kho ảo với ID ${createDto.root} không tồn tại hoặc không hợp lệ`,
        );
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_CREATION_FAILED,
        `Lỗi khi tạo thư mục: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật thông tin thư mục
   * @param id ID của thư mục
   * @param updateDto DTO chứa thông tin cập nhật thư mục
   * @param user Thông tin người dùng đã xác thực
   * @returns Thông tin thư mục đã cập nhật
   */
  @Transactional()
  async updateFolder(
    id: number,
    updateDto: UpdateFolderDto,
    user: JwtPayload
  ): Promise<FolderResponseDto> {
    const userId = user.id;
    try {
      // Kiểm tra dữ liệu đầu vào
      await this.validationHelper.validateUpdateFolder(id, updateDto, userId);

      // Lấy thông tin thư mục hiện tại
      const folder = await this.validationHelper.validateFolderExists(id);

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<Folder> = {};

      // Cập nhật tên nếu có
      if (updateDto.name !== undefined) {
        updateData.name = updateDto.name;
      }

      // Xử lý cập nhật parentId
      if (updateDto.parentId !== undefined) {
        // Xử lý parentId có thể null
        if (updateDto.parentId === null) {
          (updateData as any).parentId = null;
        } else {
          updateData.parentId = updateDto.parentId;
        }

        // Cập nhật đường dẫn thư mục dựa trên parent mới và tên hiện tại (hoặc tên mới nếu có)
        const folderName = updateDto.name !== undefined ? updateDto.name : folder.name;

        if (updateDto.parentId) {
          const parentFolder = await this.validationHelper.validateFolderExists(updateDto.parentId);
          updateData.path = parentFolder.path
            ? `${parentFolder.path}/${folderName}`
            : `/${folderName}`;
        } else {
          updateData.path = `/${folderName}`;
        }
      } else if (updateDto.name !== undefined) {
        // Nếu chỉ đổi tên mà không đổi parent, cần cập nhật lại path
        if (folder.parentId) {
          const parentFolder = await this.validationHelper.validateFolderExists(folder.parentId);
          updateData.path = parentFolder.path
            ? `${parentFolder.path}/${updateDto.name}`
            : `/${updateDto.name}`;
        } else {
          updateData.path = `/${updateDto.name}`;
        }
      }

      // Xử lý cập nhật root
      if (updateDto.root !== undefined) {
        // Xử lý root có thể null
        if (updateDto.root === null) {
          (updateData as any).root = null;
        } else {
          // Kiểm tra kho ảo tồn tại
          try {
            await this.validationHelper.validateVirtualWarehouseExists(updateDto.root);
            updateData.root = updateDto.root;
          } catch (error) {
            throw new AppException(
              BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
              `Không thể cập nhật thư mục: Kho ảo với ID ${updateDto.root} không tồn tại hoặc không hợp lệ`,
            );
          }
        }
      }

      // Cập nhật thời gian
      updateData.updatedAt = Date.now();

      // Cập nhật thư mục
      const updatedFolder = await this.folderRepository.updateFolder(id, updateData);

      // Chuyển đổi sang DTO và trả về
      return plainToInstance(FolderResponseDto, updatedFolder, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật thư mục: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      // Xử lý lỗi ràng buộc khóa ngoại cụ thể
      if (error.message && error.message.includes('folders_virtual_warehouse_warehouse_id_fk')) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
          `Lỗi khi cập nhật thư mục: Kho ảo với ID ${updateDto.root} không tồn tại hoặc không hợp lệ`,
        );
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_UPDATE_FAILED,
        `Lỗi khi cập nhật thư mục: ${error.message}`,
      );
    }
  }

  /**
   * Lấy thông tin thư mục theo ID
   * @param id ID của thư mục
   * @param user Thông tin người dùng đã xác thực
   * @returns Thông tin chi tiết của thư mục
   */
  async getFolderById(id: number, user: JwtPayload): Promise<FolderDetailResponseDto> {
    const userId = user.id;
    try {
      // Lấy thông tin thư mục với chi tiết
      const folderDetails = await this.folderRepository.findByIdWithDetails(id);

      if (!folderDetails) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_NOT_FOUND,
          `Không tìm thấy thư mục với ID ${id}`,
        );
      }

      // Kiểm tra thư mục có thuộc về người dùng không
      if (folderDetails.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
          `Thư mục với ID ${id} không thuộc về người dùng`,
        );
      }

      // Trả về thông tin chi tiết
      const result = {
        id: folderDetails.id,
        name: folderDetails.name,
        path: folderDetails.path,
        createdAt: folderDetails.createdAt,
        updatedAt: folderDetails.updatedAt,
        // Thông tin về parentId
        parentId: folderDetails.parentId,
        parentFolder: folderDetails.parentFolder ? {
          id: folderDetails.parentFolder.id,
          name: folderDetails.parentFolder.name,
          path: folderDetails.parentFolder.path
        } : null,
        // Thông tin về userId
        userId: folderDetails.userId,
        user: folderDetails.user ? {
          id: folderDetails.user.id,
          fullName: folderDetails.user.fullName,
          email: folderDetails.user.email,
          phoneNumber: folderDetails.user.phoneNumber
        } : null,
        // Thông tin về root (kho ảo)
        root: folderDetails.root,
        virtualWarehouse: folderDetails.virtualWarehouse ? {
          warehouseId: folderDetails.virtualWarehouse.warehouseId,
          associatedSystem: folderDetails.virtualWarehouse.associatedSystem,
          purpose: folderDetails.virtualWarehouse.purpose
        } : null
      };

      return plainToInstance(FolderDetailResponseDto, result, {
        excludeExtraneousValues: true,
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin thư mục: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
        `Lỗi khi lấy thông tin thư mục: ${error.message}`,
      );
    }
  }

  /**
   * Xóa thư mục
   * @param id ID của thư mục
   * @param user Thông tin người dùng đã xác thực
   * @returns Kết quả xóa
   */
  @Transactional()
  async deleteFolder(id: number, user: JwtPayload): Promise<void> {
    const userId = user.id;
    try {
      // Lấy thông tin thư mục
      const folder = await this.validationHelper.validateFolderExists(id);

      // Kiểm tra thư mục có thuộc về người dùng không
      if (folder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
          `Thư mục với ID ${id} không thuộc về người dùng`,
        );
      }

      // Kiểm tra thư mục có chứa thư mục con không
      const childFolders = await this.folderRepository.findByParentId(id);
      if (childFolders.length > 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
          `Thư mục với ID ${id} chứa thư mục con, vui lòng xóa thư mục con trước`,
        );
      }

      // Kiểm tra thư mục có chứa file không
      const files = await this.fileRepository.findByFolderId(id);
      if (files.length > 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
          `Thư mục với ID ${id} chứa file, vui lòng xóa file trước`,
        );
      }

      // Xóa thư mục
      const result = await this.folderRepository.deleteFolder(id);

      if (!result) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
          `Không thể xóa thư mục với ID ${id}`,
        );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi xóa thư mục: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_DELETE_FAILED,
        `Lỗi khi xóa thư mục: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách thư mục với phân trang và lọc
   * @param queryDto Tham số truy vấn
   * @param user Thông tin người dùng đã xác thực
   * @returns Danh sách thư mục với phân trang
   */
  async getFolders(
    queryDto: QueryFolderDto,
    user: JwtPayload
  ): Promise<PaginatedResult<FolderResponseDto>> {
    const userId = user.id;
    try {
      // Kiểm tra và xử lý trường sắp xếp
      const validSortFields = ['id', 'name', 'createdAt', 'updatedAt'];
      if (queryDto.sortBy && !validSortFields.includes(queryDto.sortBy)) {
        // Nếu trường sắp xếp không hợp lệ, sử dụng trường mặc định
        queryDto.sortBy = 'id';
      }

      // Thêm userId vào query để chỉ lấy thư mục của người dùng hiện tại
      queryDto.userId = userId;

      // Lấy danh sách thư mục từ repository
      const result = await this.folderRepository.findAll_user(queryDto);

      // Chuyển đổi các item sang DTO
      const items = result.items.map(folder =>
        plainToInstance(FolderResponseDto, folder, {
          excludeExtraneousValues: true,
        }),
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách thư mục: ${error.message}`, error.stack);

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
        `Lỗi khi lấy danh sách thư mục: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách thư mục gốc (không có thư mục cha) của người dùng
   * @param user Thông tin người dùng đã xác thực
   * @returns Danh sách thư mục gốc
   */
  async getRootFolders(user: JwtPayload): Promise<FolderResponseDto[]> {
    const userId = user.id;
    try {
      // Lấy danh sách thư mục gốc từ repository
      const rootFolders = await this.folderRepository.findRootFoldersByUserId(userId);

      // Chuyển đổi các item sang DTO
      return rootFolders.map(folder =>
        plainToInstance(FolderResponseDto, folder, {
          excludeExtraneousValues: true,
        }),
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách thư mục gốc: ${error.message}`, error.stack);

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
        `Lỗi khi lấy danh sách thư mục gốc: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách thư mục con của một thư mục
   * @param parentId ID của thư mục cha
   * @param user Thông tin người dùng đã xác thực
   * @returns Danh sách thư mục con
   */
  async getChildFolders(parentId: number, user: JwtPayload): Promise<FolderResponseDto[]> {
    const userId = user.id;
    try {
      // Kiểm tra thư mục cha tồn tại
      const parentFolder = await this.validationHelper.validateFolderExists(parentId);

      // Kiểm tra thư mục cha có thuộc về người dùng không
      if (parentFolder.userId !== userId) {
        throw new AppException(
          BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
          `Thư mục cha với ID ${parentId} không thuộc về người dùng`,
        );
      }

      // Lấy danh sách thư mục con từ repository
      const childFolders = await this.folderRepository.findByParentId(parentId);

      // Chuyển đổi các item sang DTO
      return childFolders.map(folder =>
        plainToInstance(FolderResponseDto, folder, {
          excludeExtraneousValues: true,
        }),
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách thư mục con: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.FOLDER_FETCH_FAILED,
        `Lỗi khi lấy danh sách thư mục con: ${error.message}`,
      );
    }
  }
}