# Đăng đơn (E-Logistic)

## API lấy danh sách giải pháp
- **Phương thức:** GET  
- **Đường dẫn:** `/open/api/v1/shop/solutions` ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=)).  
- **Mô tả:** Tr<PERSON> về danh sách các giải pháp (gói bảo hiểm/phụ phí) mà shop có thể áp dụng.  

- **<PERSON><PERSON><PERSON> cầu (Request):**  
  - Header:
    - `Token: {API_TOKEN}` – mã token API của shop (xác thực) ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=)).  
    - `X-Client-Source: {PARTNER_CODE}` – mã cửa hàng hoặc mã đối tác cung cấp bởi GHTK ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=)).  
    - `Content-Type: application/json` (định dạng mặc định là JSON) ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=)).  
  - Không có tham số đường dẫn hay tham số truy vấn khác (GET với URL trên).  
- **Ví dụ Request:** (cURL)  
  ```bash
  curl --location '{OPEN_API}/open/api/v1/shop/solutions' \
    --header 'Token: {API_TOKEN}' \
    --header 'X-Client-Source: {PARTNER_CODE}' \
    --header 'Content-Type: application/json'
  ```  

- **Phản hồi (Response):**  
  - **Thành công:** HTTP 200, trả về JSON gồm trường `data` là mảng các giải pháp. Mỗi giải pháp có các trường:  
    - `solution_id` (Long) – mã định danh của giải pháp ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=Parameter%20Datatype%20Description%20solution_id%20Long,String%20T%C3%AAn%20nh%C3%B3m%20gi%E1%BA%A3i%20ph%C3%A1p)).  
    - `description` (String) – mô tả chi tiết về giải pháp (độ an toàn, bảo hiểm,…).  
    - `group_name` (String) – tên nhóm giải pháp (ví dụ: “Gói giải pháp an toàn hàng hoá toàn diện”) ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=%7B%20,)) ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=Parameter%20Datatype%20Description%20solution_id%20Long,String%20T%C3%AAn%20nh%C3%B3m%20gi%E1%BA%A3i%20ph%C3%A1p)).  
  - **Ví dụ Response thành công:**  
    ```json
    {
      "success": true,
      "data": [
        {
          "solution_id": 1340164168838205440,
          "description": "Giúp shop đảm bảo ngoại quan sản phẩm ...",
          "group_name": "Gói giải pháp an toàn hàng hoá toàn diện"
        },
        {
          "solution_id": 1341971770371706880,
          "description": "Giúp shop đảm bảo ....",
          "group_name": "Gói giải pháp an toàn hàng hoá toàn diện"
        }
      ],
      "message": "Thành công!",
      "code": 200,
      "rid": "dd663be1c8d0"
    }
    ```  
    Trong đó, `data` là mảng các giải pháp, `message` là thông báo (có thể rỗng nếu thành công). Ví dụ trên cho thấy hai giải pháp mẫu ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=%7B%20,)).
  - **Thất bại:** Nếu không có giải pháp nào, `success` vẫn là `true` nhưng mảng `data` rỗng (kèm `code: 200`) ([3. API lấy danh sách giải pháp | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/solutions#:~:text=)).  
- **Lưu ý:** API này không có tham số bổ sung (ngoài Header) và luôn trả về danh sách (có thể rỗng). Các mã `solution_id` có thể sử dụng khi đăng đơn có gắn giải pháp tương ứng.

## API đăng đơn hàng
- **Phương thức:** POST  
- **Đường dẫn:** `/services/shipment/order` (có thể thêm tham số `ver` để chỉ định phiên bản API) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=%C4%90%C6%B0%E1%BB%9Dng%20d%E1%BA%ABn)).  
- **Mô tả:** Đối tác gửi thông tin đơn hàng (có thể nhiều đơn trong một request) lên hệ thống GHTK. Sau khi thành công, hệ thống sẽ trả về thông tin các đơn hàng đã lưu, bao gồm mã đơn GHTK (label) và các thông tin liên quan ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=%C4%90%C6%B0%E1%BB%9Dng%20d%E1%BA%ABn)).  

- **Yêu cầu (Request):**  
  - Header:
    - `Token: {API_TOKEN}` – mã token API cấp cho shop ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=Headers)).  
    - `X-Client-Source: {PARTNER_CODE}` – mã cửa hàng/đối tác do GHTK cung cấp ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=Headers)).  
    - `Content-Type: application/json` – định dạng JSON cho nội dung gửi.  
  - **Body (JSON):** gồm hai phần chính:
    - `products`: mảng các sản phẩm trong đơn (ít nhất 1 sản phẩm) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=POST%20%2Fservices%2Fshipment%2Forder%2F%3Fver%3D1.5%20HTTP%2F1.1%20Token%3A%20APITokenSample,0.1)) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,sa%CC%81ch%20th%C3%B4ng%20tin%20s%E1%BA%A3n%20ph%E1%BA%A9m)). Mỗi phần tử có các trường:
      - `name` (String, **bắt buộc**) – tên hàng hóa ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,sa%CC%81ch%20th%C3%B4ng%20tin%20s%E1%BA%A3n%20ph%E1%BA%A9m)).  
      - `price` (Integer, không bắt buộc) – giá trị của hàng hóa.  
      - `weight` (Double, **bắt buộc**) – cân nặng hàng hóa (kg) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=price%20no%20Integer%20,sa%CC%81ch%20th%C3%B4ng%20tin%20s%E1%BA%A3n%20ph%E1%BA%A9m)).  
      - `quantity` (Integer, không bắt buộc) – số lượng sản phẩm.  
      - `product_code` (String, không bắt buộc) – mã sản phẩm do shop hoặc GHTK cấp (nếu đã có) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=quantity%20no%20Integer%20,sa%CC%81ch%20th%C3%B4ng%20tin%20s%E1%BA%A3n%20ph%E1%BA%A9m)).  
    - `order`: đối tượng mô tả thông tin đơn hàng ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,0911222333)) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.weight_option%20no%20String%20,%C4%91%E1%BB%8Bnh%20GHTK%20set%20theo%20ca)). Bao gồm các trường chính:
      - `id` (String, **bắt buộc**) – mã đơn hàng bên đối tác (tùy chỉnh).  
      - `pick_name` (String, **bắt buộc**) – tên người lấy hàng (tại kho shop) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,0911222333)).  
      - `pick_address` (String, **bắt buộc**) – địa chỉ lấy hàng (tại kho shop) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,0911222333)).  
      - `pick_province`, `pick_district`, `pick_ward` (String, **bắt buộc**) – tỉnh/thành, quận/huyện, phường/xã lấy hàng (theo nơi kho shop) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,0911222333)).  
      - `pick_tel` (String, **bắt buộc**) – số điện thoại người lấy hàng (kho).  
      - `name` (String, **bắt buộc**) – tên người nhận hàng.  
      - `address` (String, **bắt buộc**) – địa chỉ nhận hàng (chi tiết) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=%22name%22%3A%20%22GHTK%20,Kh%C3%A1c)).  
      - `province`, `district`, `ward`, `hamlet` (String, **bắt buộc** với `province` và `district`; `ward`, `hamlet` có thể không bắt buộc nếu dùng địa chỉ cấp 4) – thông tin vùng nhận hàng ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=%22name%22%3A%20%22GHTK%20,Kh%C3%A1c)).  
      - `tel` (String, **bắt buộc**) – số điện thoại người nhận.  
      - `is_freeship` (String, không bắt buộc) – xác định đơn freeship (`"1"` là freeship, `"0"` hoặc không gửi là tính phí bình thường).  
      - `pick_date` (String, định dạng `YYYY-MM-DD`, không bắt buộc) – ngày hẹn lấy hàng ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=%22is_freeship%22%3A%20%221%22%2C%20%22pick_date%22%3A%20%222016,b%E1%BA%AFt%20bu%E1%BB%99c%20pick_option%20l%C3%A0%20COD)).  
      - `pick_money` (Integer, không bắt buộc) – số tiền thu hộ (COD) từ người nhận (đơn vị VNĐ) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.value%20yes%20Interger%20%28VN%C4%90%29%20,se%CC%83%20g%C6%B0%CC%89i%20ta%CC%A3i%20b%C6%B0u%20cu%CC%A3c)).  
      - `note` (String, không bắt buộc) – ghi chú đơn hàng (ví dụ: *"Khối lượng tính cước tối đa: 1.00 kg"*).  
      - `value` (Integer, **bắt buộc**) – giá trị tính khai giá của đơn (đơn vị VNĐ) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.value%20yes%20Interger%20%28VN%C4%90%29%20,se%CC%83%20g%C6%B0%CC%89i%20ta%CC%A3i%20b%C6%B0u%20cu%CC%A3c)).  
      - `transport` (String, không bắt buộc) – phương thức vận chuyển: `"fly"` (đường bay) hoặc `"road"` (đường bộ), mặc định GHTK sẽ chọn phù hợp ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=h%C3%A0ng%2C%20m%E1%BA%B7c%20%C4%91%E1%BB%8Bnh%20l%C3%A0%20%C4%91%C6%B0%E1%BB%9Dng,nh%E1%BA%A3y%20v%E1%BB%81%20PTVC%20m%E1%BA%B7c%20%C4%91%E1%BB%8Bnh)).  
      - `pick_option` (String, không bắt buộc) – cách lấy hàng, một trong `"cod"` (thu hộ) hoặc `"post"` (đối soát), mặc định `"cod"` ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.opm%20no%20Interger%20%28VN%C4%90%29%20,se%CC%83%20g%C6%B0%CC%89i%20ta%CC%A3i%20b%C6%B0u%20cu%CC%A3c)).  
      - `deliver_option` (String, không bắt buộc) – nếu sử dụng dịch vụ xFast/xTeam, gán giá trị `"xteam"` ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.deliver_option%20no%20String%20,c%C3%A2y%20c%E1%BB%91i%20cho%20%C4%91%C6%A1n%20h%C3%A0ng)).  
      - `tags` (Array Integer, không bắt buộc) – mã nhãn (tag) gán cho đơn, ví dụ 1 (hàng dễ vỡ), 2 (giá trị cao),... Mỗi mã tag được mô tả chi tiết trong tài liệu nhãn ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=M%C3%B4%20t%E1%BA%A3%20nh%C3%A3n%20%C4%91%C6%A1n%20h%C3%A0ng,tags)).  
      - `sub_tags` (Array Integer, không bắt buộc) – chi tiết nhãn phụ (dùng trong trường hợp nhãn cây cối) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=order.tags%20no%20Array%20,c%C3%A2y%20c%E1%BB%91i%20cho%20%C4%91%C6%A1n%20h%C3%A0ng)) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=M%C3%B4%20t%E1%BA%A3%20nh%C3%A3n%20%C4%91%C6%A1n%20h%C3%A0ng,sub_tags)).  
  - **Ví dụ Request (JSON):**  
    ```json
    {
      "products": [
        {"name": "bút", "weight": 0.1, "quantity": 1, "product_code": 1241},
        {"name": "tẩy", "weight": 0.2, "quantity": 1, "product_code": 1254}
      ],
      "order": {
        "id": "a4",
        "pick_name": "Kho HCM nội thành",
        "pick_address": "590 CMT8 P.11",
        "pick_province": "TP. Hồ Chí Minh",
        "pick_district": "Quận 3",
        "pick_ward": "Phường 1",
        "pick_tel": "0911222333",
        "name": "GHTK - HCM - Nội thành",
        "address": "123 Nguyễn Chí Thanh",
        "province": "TP. Hồ Chí Minh",
        "district": "Quận 1",
        "ward": "Phường Bến Nghé",
        "hamlet": "Khác",
        "is_freeship": "1",
        "pick_date": "2016-09-30",
        "pick_money": 47000,
        "note": "Khối lượng tính cước tối đa: 1.00 kg",
        "value": 3000000,
        "transport": "fly",
        "pick_option": "cod",
        "deliver_option": "xteam"
      }
    }
    ```  
    Đoạn JSON trên minh hoạ cách truyền `products` và `order` với các trường cơ bản ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,1254)) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,0911222333)).  

- **Phản hồi (Response):**  
  - **Thành công:** HTTP 200, JSON ví dụ:  
    ```json
    {
      "success": true,
      "message": "",
      "order": {
        "partner_id": "123123a",
        "label": "S1.A1.2001297581",
        "area": "1",
        "fee": "30400",
        "insurance_fee": "15000",
        "tracking_id": 2001297581,
        "estimated_pick_time": "Sáng 2017-07-01",
        "estimated_deliver_time": "Chiều 2017-07-01",
        "products": [],
        "status_id": 2
      }
    }
    ```  
    - `success: true` báo đăng đơn thành công.  
    - `order.partner_id`: mã đơn của đối tác (giá trị `order.id` gửi lên) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,2001297581)).  
    - `order.label`: mã vận đơn (label) do GHTK cấp cho đơn hàng ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,2001297581)).  
    - `order.tracking_id`: mã số tracking của GHTK (thường là phần sau label) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,2001297581)).  
    - Các trường khác: `area` (khu vực), `fee` (phí ship), `insurance_fee` (phí khai giá), `estimated_pick_time`, `estimated_deliver_time`, `status_id` (mã trạng thái đơn sau khi tạo), v.v. Ví dụ trên thể hiện đơn đã được nhận (status_id=2) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=,01%22%2C%20%22products%22%3A%20%5B%5D%2C%20%22status_id%22%3A%202)).  
  - **Thất bại:** HTTP 200 kèm `"success": false` nếu có lỗi, ví dụ:  
    ```json
    {
      "success": false,
      "message": "Chưa có thông tin order"
    }
    ```  
    - `message` chứa mô tả lỗi (ví dụ thiếu thông tin, định dạng sai) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=)).  
- **Lưu ý:**  
  - Các trường `tags` và `sub_tags` liên quan đến nhãn đơn hàng cần tuân theo bảng nhãn (ví dụ tag=1 cho hàng dễ vỡ) ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=M%C3%B4%20t%E1%BA%A3%20nh%C3%A3n%20%C4%91%C6%A1n%20h%C3%A0ng,tags)). Tuy nhiên, kể từ 01/01/2026 GHTK **ngừng hỗ trợ** trường `order.tags` và `order.sub_tags` đối với các đơn mới; thay vào đó đối tác nên sử dụng “gói giải pháp” thay thế ([3. API đăng đơn Express | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/submit-order-express#:~:text=c%E1%BA%A3nh%20b%C3%A1o)).  
  - Trường `pick_money`: nếu gửi 0 hoặc không gửi, mặc định GHTK thu người nhận bằng tổng (COD + phí ship).  
  - Trường `transport`: nếu giá trị không hợp lệ, GHTK sẽ tự động chọn PTVC (phương thức vận chuyển) mặc định.  
  - Đảm bảo gửi đúng token và mã cửa hàng trong header để xác thực yêu cầu.

## API tính phí đơn hàng
- **Phương thức:** GET  
- **Đường dẫn:** `/services/shipment/fee` ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=%C4%90%C6%B0%E1%BB%9Dng%20d%E1%BA%ABn)).  
- **Mô tả:** Tính toán chi phí vận chuyển và các phụ phí (nếu có) cho một đơn hàng dựa trên thông tin địa chỉ, khối lượng, giá trị hàng và tùy chọn dịch vụ (xTeam/xFast,…).  
- **Yêu cầu (Request):**  
  - Header:
    - `Token: {API_TOKEN}`, `X-Client-Source: {PARTNER_CODE}` như trên ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=Headers)).  
  - Tham số truy vấn (query parameters) gồm:  
    - `pick_address_id` (String, không bắt buộc) – mã địa chỉ lấy hàng của shop (nếu có). Nếu có giá trị hợp lệ, GHTK sẽ ưu tiên dùng địa chỉ lấy hàng này và **bỏ qua** `pick_province`, `pick_district` ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=th%C3%B4ng%20tin)).  
    - `pick_address` (String, không bắt buộc) – địa chỉ ngắn gọn nơi lấy hàng (ví dụ “nhà số 5, tổ 3, ngách 11, ngõ 45”).  
    - `pick_province` (String, **bắt buộc**) – tỉnh/thành nơi lấy hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,%C4%91%C6%B0%E1%BB%A3c%20%C6%B0u%20ti%C3%AAn%20s%E1%BB%AD%20d%E1%BB%A5ng)).  
    - `pick_district` (String, **bắt buộc**) – quận/huyện lấy hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=5%2C%20t%E1%BB%95%203%2C%20ng%C3%A1ch%2011%2C,c%E1%BB%A7a%20ng%C6%B0%E1%BB%9Di%20nh%E1%BA%ADn%20h%C3%A0ng%20h%C3%B3a)).  
    - `pick_ward` (String, không bắt buộc) – phường/xã lấy hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=pick_province%20yes%20String%20,c%E1%BB%A7a%20ng%C6%B0%E1%BB%9Di%20nh%E1%BA%ADn%20h%C3%A0ng%20h%C3%B3a)).  
    - `pick_street` (String, không bắt buộc) – đường/phố lấy hàng.  
    - `address` (String, không bắt buộc) – địa chỉ chi tiết người nhận (nếu khác `province`/`district` chính). Ví dụ “Chung cư CT1, ngõ 58, Trần Bình” ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=pick_ward%20no%20String%20,Gram)).  
    - `province` (String, **bắt buộc**) – tỉnh/thành người nhận hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=address%20no%20String%20,t%C3%ADnh%20ph%C3%AD%20khai%20gi%C3%A1%2C%20%C4%91%C6%A1n)).  
    - `district` (String, **bắt buộc**) – quận/huyện người nhận hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=ng%C3%B5%2058%2C%20%C4%91%C6%B0%E1%BB%9Dng%20Tr%E1%BA%A7n%20B%C3%ACnh,c%E1%BB%A7a%20ng%C6%B0%E1%BB%9Di%20nh%E1%BA%ADn%20h%C3%A0ng%20h%C3%B3a)).  
    - `ward` (String, không bắt buộc) – phường/xã người nhận hàng ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=province%20yes%20String%20,c%E1%BB%A7a%20ng%C6%B0%E1%BB%9Di%20nh%E1%BA%ADn%20h%C3%A0ng%20h%C3%B3a)).  
    - `street` (String, không bắt buộc) – đường/phố người nhận.  
    - `weight` (Integer, **bắt buộc**) – tổng cân nặng gói hàng (gram) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=weight%20yes%20Integer%20,N%E1%BA%BFu)).  
    - `value` (Integer, không bắt buộc) – giá trị đơn hàng (VNĐ) dùng để tính phí khai giá ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=weight%20yes%20Integer%20,N%E1%BA%BFu)).  
    - `transport` (String, không bắt buộc) – phương thức vận chuyển: `road` hoặc `fly`. Nếu không hợp lệ, GHTK sẽ chọn phương thức mặc định dựa trên loại dịch vụ.  
    - `deliver_option` (String, **bắt buộc**) – nếu sử dụng xFast/xTeam, giá trị `"xteam"`; nếu không dùng, gửi `"none"` hoặc không gửi ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=ph%C6%B0%C6%A1ng%20th%E1%BB%A9c%20v%E1%BA%ADn%20chuy%E1%BB%83n%20kh%C3%B4ng,%C4%91%C6%A1n%20h%C3%A0ng%20v%C3%A0o%20m%E1%BA%A3ng%20tags)).  
    - `tags` (array, không bắt buộc) – mảng mã nhãn áp dụng cho đơn (ví dụ [1,7]).  
  - **Ví dụ Request:**  
    ```
    GET /services/shipment/fee?address=P.503%20t%C3%B2a%20nh%C3%A0%20Auu%20Vi%E1%BB%87t,%20s%E1%BB%91%201%20L%C3%AA%20%C4%90%E1%BB%A9c%20Th%E1%BB%8D
    &province=H%C3%A0%20N%E1%BB%99i&district=Qu%E1%BA%ADn%20C%E1%BA%A7u%20Gi%E1%BA%A5y
    &pick_province=H%C3%A0%20N%E1%BB%99i&pick_district=Qu%E1%BA%ADn%20Hai%20B%C3%A0%20Tr%C6%B0ng
    &weight=1000&value=3000000&deliver_option=xteam
    ```  
    Trong ví dụ, các tham số đã encode URL, tương ứng với pick at: “Hà Nội, Quận Hai Bà Trưng”, nhận tại “Hà Nội, Quận Cầu Giấy”, khối lượng 1000g, giá trị 3,000,000đ, dịch vụ xTeam ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=GET%20%2Fservices%2Fshipment%2Ffee%3Faddress%3DP.503,Source%3A%20%7BPARTNER_CODE)).  
- **Phản hồi (Response):**  
  - **Trường thông tin trả về:** (đối tượng `fee`)  
    - `fee.name` (String) – tên gói cước áp dụng (ví dụ `"area1"`, `"area2"`, `"area3"`) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=Tham%20s%E1%BB%91%20M%C3%B4%20t%E1%BA%A3%20fee,false)).  
    - `fee.fee` (Integer) – cước vận chuyển (VNĐ) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=%7B%20,15000)).  
    - `fee.insurance_fee` (Integer) – phí khai giá (VNĐ) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=,15000)).  
    - `fee.delivery` (Boolean) – khả năng giao tới khu vực này (`true` nếu GHTK hỗ trợ giao, ngược lại `false`) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=,only_hanoi)).  
    - `fee.delivery_type`, `fee.extFees` (Array) – có thể bao gồm thông tin chi tiết phụ phí (ví dụ: hàng dễ vỡ, hàng khô) ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=,fragile)). Mỗi phần tử trong `extFees` có `title` (tên phụ phí), `amount` (số tiền phụ phí).  
  - **Ví dụ Response thành công:**  
    ```json
    {
      "success": true,
      "message": "",
      "fee": {
        "name": "area1",
        "fee": 30400,
        "insurance_fee": 15000,
        "delivery_type": "only_hanoi",
        "a": 3,
        "dt": "local",
        "extFees": [
          {
            "display": "(+ 7,400 đ)",
            "title": "Phụ phí hàng dễ vỡ",
            "amount": 7400,
            "type": "fragile"
          },
          {
            "display": "(+ 13,400 đ)",
            "title": "Phụ phí hàng nông sản/thực phẩm khô",
            "amount": 13400,
            "type": "food"
          }
        ],
        "delivery": true
      }
    }
    ```  
    Trong ví dụ này, GHTK áp dụng gói cước `"area1"`, phí ship 30.400đ, phí khai giá 15.000đ, `delivery: true` (có thể giao được). Phụ phí đóng gói dễ vỡ 7.400đ, hàng khô 13.400đ được liệt kê trong `extFees` ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=,fragile)).  
  - **Lưu ý:**  
    - Nếu đã gửi `pick_address_id` hợp lệ, thông tin tỉnh/quận từ địa chỉ lấy hàng sẽ được tự động lấy từ mã đó, khi đó `pick_province`, `pick_district` không bắt buộc ([4. API tính phí đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/calculate-shipping-fee#:~:text=th%C3%B4ng%20tin)).  
    - Để tính phí xFast/xTeam, luôn gửi `deliver_option=xteam` cùng yêu cầu (hoặc `none` nếu không sử dụng xFast). Nếu thiếu, GHTK sẽ tính phí chuẩn.  

## API lấy trạng thái đơn hàng
- **Phương thức:** GET  
- **Đường dẫn:** `/services/shipment/v2/{TRACKING_ORDER}` ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=%C4%90%C6%B0%E1%BB%9Dng%20d%E1%BA%ABn)). Tham số `{TRACKING_ORDER}` có thể là mã vận đơn GHTK (`label_id`) hoặc `partner_id` (mã đơn đối tác gửi ở trường `order.id`) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=Tham%20s%E1%BB%91)).  
- **Mô tả:** Lấy thông tin trạng thái hiện tại của một đơn hàng đã được GHTK nhận (giao hàng) dựa trên mã đơn. Khách hàng dùng mã đơn để kiểm tra bước giao tiếp theo của đơn.  

- **Yêu cầu (Request):**  
  - Header: `Token: {API_TOKEN}`, `X-Client-Source: {PARTNER_CODE}` như trên ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=Headers)).  
  - Đường dẫn ví dụ:  
    ```
    GET /services/shipment/v2/S1.A1.17373471 HTTP/1.1
    Token: {API_TOKEN}
    X-Client-Source: {PARTNER_CODE}
    ```  
- **Phản hồi (Response):**  
  - **Các trường trả về (trong đối tượng `order`):** ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=Tham%20s%E1%BB%91%20M%C3%B4%20t%E1%BA%A3%20label_id,Ghi%20ch%C3%BA%20c%E1%BB%A7a%20%C4%91%C6%A1n%20h%C3%A0ng))  
    - `order.label_id` (String) – mã vận đơn GHTK (ví dụ `"S1.A1.17373471"`) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=Tham%20s%E1%BB%91%20M%C3%B4%20t%E1%BA%A3%20label_id,b%E1%BA%A3ng%20m%C3%A3%20tr%E1%BA%A1ng%20th%C3%A1i%20%C4%91%C6%A1n)).  
    - `order.partner_id` (String) – mã đơn đối tác (trường `id` đã gửi khi đăng đơn) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=label_id%20String%20,m%C3%A3%20tr%E1%BA%A1ng%20th%C3%A1i%20%C4%91%C6%A1n%20h%C3%A0ng)).  
    - `order.status` (String) – mã trạng thái đơn trên GHTK (tham khảo bảng mã trạng thái) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=partner_id%20String%20,DD%20hh%3Amm%3Ass)).  
    - `order.status_text` (String) – mô tả trạng thái (ví dụ “Chưa tiếp nhận”) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=partner_id%20String%20,DD%20hh%3Amm%3Ass)).  
    - `order.created`/`order.modified` (String) – thời gian tạo và cập nhật gần nhất của đơn (định dạng `YYYY-MM-DD hh:mm:ss`) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=created%20String%20,Ghi%20ch%C3%BA%20c%E1%BB%A7a%20%C4%91%C6%A1n%20h%C3%A0ng)).  
    - `order.message` (String) – ghi chú/tình trạng cụ thể (nếu có) của đơn.  
    - `order.pick_date`/`order.deliver_date` (String) – ngày hẹn lấy/giao nếu có.  
    - `order.customer_fullname`, `order.customer_tel`, `order.address` – thông tin người nhận (họ tên, điện thoại, địa chỉ) ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=pick_date%20String%20,Ph%C3%AD%20giao%20h%C3%A0ng)).  
    - `order.ship_money` (Integer) – phí ship của đơn (VNĐ).  
    - `order.insurance` (Integer) – phí khai giá (VNĐ).  
    - `order.value` (Integer) – giá trị đã khai báo (VNĐ).  
    - `order.weight` (Integer) – khối lượng đơn (gram).  
    - `order.pick_money` (Integer) – số tiền COD (nếu có thu hộ).  
    - `order.is_freeship` (Integer) – thông tin freeship (1 = freeship, 0 = không).  
    - `order.status_id` (Integer) – mã trạng thái (giống `status`).  
    - Ngoài ra còn `storage_day`, `return_order` (nếu đã trả hàng), v.v. (tuỳ từng trạng thái).  
  - **Ví dụ Response thành công:**  
    ```json
    {
      "success": true,
      "message": "",
      "order": {
        "label_id": "S1.A1.17373471",
        "partner_id": "1234567",
        "status": "1",
        "status_text": "Chưa tiếp nhận",
        "created": "2016-10-31 22:32:08",
        "modified": "2016-10-31 22:32:08",
        "message": "Không giao hàng 1 phần",
        "pick_date": "2017-09-13",
        "deliver_date": "2017-09-14",
        "customer_fullname": "Vân Nguyễn",
        "customer_tel": "0911222333",
        "address": "123 Nguyễn Chí Thanh, Quận 1, TP Hồ Chí Minh",
        "storage_day": 0,
        "ship_money": 50000,
        "insurance": 15000,
        "value": 2000000,
        "weight": 500,
        "pick_money": 30000,
        "is_freeship": 0,
        "status_id": 1
      }
    }
    ```  
    Đơn vị `status`/`status_id` = 1 nghĩa là “Chưa tiếp nhận” ([5. API lấy trạng thái đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/tracking-status#:~:text=%7B%20,31%2022%3A32%3A08)). Các trường khác tương ứng thông tin đơn hàng đã lưu.  
  - **Lưu ý:** Nếu mã đơn không tồn tại hoặc sai format, API sẽ trả về `success: false` và `message` thông báo lỗi. Không có nội dung phức tạp khác.  

## API in nhãn đơn hàng
- **Phương thức:** GET  
- **Đường dẫn:** `/services/label/{TRACKING_ORDER}` với truy vấn `?original={ORIGINAL}&paper_size={PAPER_SIZE}` tuỳ chọn ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=GET%20%2Fservices%2Flabel%2F)).  
  - `{TRACKING_ORDER}` là mã vận đơn GHTK (không dùng mã đối tác ở API này) ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,%28m%E1%BA%B7c%20%C4%91%E1%BB%8Bnh%20l%C3%A0%20A6)).  
  - `original` (String, không bắt buộc) – kiểu in: `"portrait"` (dọc) hoặc `"landscape"` (ngang), mặc định `"portrait"` ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,%28m%E1%BA%B7c%20%C4%91%E1%BB%8Bnh%20l%C3%A0%20A6)).  
  - `paper_size` (String, không bắt buộc) – kích thước giấy: `"A5"` hoặc `"A6"`, mặc định `"A6"` ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,%28m%E1%BA%B7c%20%C4%91%E1%BB%8Bnh%20l%C3%A0%20A6)).  
- **Mô tả:** Trả về file PDF chứa nhãn dán lên kiện hàng của đơn (bao gồm mã vạch, thông tin khách, sp, phương thức vận chuyển,...). GHTK hỗ trợ 2 mẫu nhãn (A5 ngang hoặc dọc) và hai khổ giấy (A5/A6) ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=M%E1%BA%ABu%20label%20A5%20kh%E1%BB%95%20ngang%3A)).  

- **Yêu cầu (Request):**  
  - Header: `Token` và `X-Client-Source` như trên ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=Token%3A%20%7BAPI_TOKEN%7D%20X)).  
  - Ví dụ:  
    ```
    GET /services/label/S1.A1.2001297581?original=portrait&paper_size=A5 HTTP/1.1
    Token: {API_TOKEN}
    X-Client-Source: {PARTNER_CODE}
    ```  
- **Phản hồi (Response):**  
  - Nếu thành công, trả về file PDF dạng nhãn (HTTP 200, header `Content-Type: application/pdf; Content-Disposition: attachment`) ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=)).  
  - **Ví dụ Response thành công:**  
    ```
    HTTP/1.1 200 OK
    Content-Type: application/pdf
    Content-Disposition: attachment; filename=""
    Content-Transfer-Encoding: binary
    (nội dung PDF nhị phân của nhãn đơn)
    ```  
  - **Nếu lỗi:** Ví dụ khi mã vận đơn không hợp lệ, trả về JSON lỗi:  
    ```json
    {
      "success": false,
      "message": "Mã vận đơn không hợp lệ, không tìm thấy vận đơn"
    }
    ``` ([6. API in đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/print-label#:~:text=Tr%C6%B0%E1%BB%9Dng%20h%E1%BB%A3p%20c%C3%B3%20l%E1%BB%97i)).  
- **Lưu ý:** Khách hàng lấy file PDF ở body phản hồi (thường cho phép download). Không có JSON thành công nếu lấy nhãn được; chỉ JSON lỗi khi thất bại.

## API huỷ đơn
- **Phương thức:** **POST** (endpoint ghi là POST, tuy mẫu code nhầm ghi GET) ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=,%C4%91%C6%A1n%20GHTK)) ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=Code)).  
- **Đường dẫn:**  
  ```
  POST /services/shipment/cancel/{TRACKING_ORDER}
  ```  
  - Nếu huỷ bằng mã đối tác, định dạng `{TRACKING_ORDER}` phải là `partner_id:{PARTNER_CODE}` ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=warning)). Ví dụ: `/services/shipment/cancel/partner_id:123ABC`.  
- **Mô tả:** Hủy đơn hàng đã được gửi lên GHTK (trừ các đơn đã ở các trạng thái không thể huỷ) ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=c%E1%BA%A3nh%20b%C3%A1o)).  

- **Yêu cầu (Request):**  
  - Header: `Token`, `X-Client-Source` như trên ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=%2A%20Token%3A%20%60%7BAPI_TOKEN%7D%60%20%2A%20X,Type%3A%20%60application%2Fjson)).  
  - Đường dẫn ví dụ: `POST /services/shipment/cancel/S1.A1.2001297581`.  
- **Phản hồi (Response):**  
  - **Huỷ thành công:**  
    ```json
    {
      "success": true,
      "message": "",
      "log_id": "..."
    }
    ``` ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=Hu%E1%BB%B7%20th%C3%A0nh%20c%C3%B4ng)) – `success: true` và một `log_id` để đối chiếu.  
  - **Đơn đã huỷ trước đó:**  
    ```json
    {
      "success": false,
      "message": "Đơn hàng đã ở trạng thái hủy",
      "log_id": "..."
    }
    ``` ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=%C4%90%C6%A1n%20h%C3%A0ng%20%C4%91%C3%A3%20hu%E1%BB%B7)).  
  - **Không thể huỷ (ví dụ đã lấy hàng):**  
    API chỉ chấp nhận huỷ khi đơn đang ở trạng thái 1 (chưa nhận), 2 (đã nhận) hoặc 12 (đang lấy hàng) ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=C%C3%A1c%20tr%E1%BA%A1ng%20th%C3%A1i%20sau%20khi,c%C3%A1c%20tr%E1%BA%A1ng%20th%C3%A1i%20sau)). Nếu đơn ở trạng thái khác (ví dụ đã lấy hàng, giao thành công), trả về:  
    ```json
    {
      "success": false,
      "message": "Đơn đã lấy hàng, không thể hủy đơn.",
      "log_id": "..."
    }
    ``` ([7. API huỷ đơn hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-cancel-order#:~:text=,12)).  
- **Lưu ý:** Khi hủy thành công, GHTK cập nhật lại tình trạng đơn. Trường `message` có thể rỗng hoặc chứa lý do do đối tác cung cấp khi huỷ (nếu có).  

# Địa chỉ (Address)

## API danh sách địa chỉ kho hàng (địa chỉ lấy hàng)
- **Phương thức:** GET  
- **Đường dẫn:** `/services/shipment/list_pick_add` ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=GET%20%2Fservices%2Fshipment%2Flist_pick_add)).  
- **Mô tả:** Trả về danh sách các địa chỉ kho hàng (điểm lấy hàng) đã được shop cài đặt trên hệ thống quản lý GHTK (qua giao diện Khách hàng) ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=9,%C4%91i%CC%A3a%20chi%CC%89%20l%C3%A2%CC%81y%20ha%CC%80ng)). Dùng để tham khảo thay vì gửi tay địa chỉ.  

- **Yêu cầu (Request):**  
  - Header: `Token`, `X-Client-Source` như trên ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=Headers)).  
  - Không có tham số bổ sung (GET không có query) ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=GET%20%2Fservices%2Fshipment%2Flist_pick_add)).  
- **Phản hồi (Response):**  
  - Trả về JSON với `data` là mảng địa chỉ kho. Mỗi địa chỉ gồm các trường:  
    - `pick_address_id` (String) – mã địa chỉ (ID) trong hệ thống GHTK ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=%7B%20,0987654321)).  
    - `address` (String) – địa chỉ chi tiết (dùng để hiển thị) ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=%7B%20,0987654321)).  
    - `pick_tel` (String) – số điện thoại liên hệ tại kho ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=%7B%20,Store%201)).  
    - `pick_name` (String) – tên kho hoặc tên người phụ trách kho ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=%7B%20,Store%201)).  
  - **Ví dụ Response:**  
    ```json
    {
      "success": true,
      "message": "",
      "data": [
        {
          "pick_address_id": "88256",
          "address": "Số nhà 105, ngõ 13 Lĩnh Nam, Mai Động, Hoàng Mai, Hà Nội",
          "pick_tel": "0987654321",
          "pick_name": "Store 1"
        },
        {
          "pick_address_id": "88260",
          "address": "1312, Phường 1, Quận Bình Thạnh, TP Hồ Chí Minh",
          "pick_tel": "0987654321",
          "pick_name": "Store 2"
        }
      ]
    }
    ```  
    (Ví dụ trên có 2 kho địa chỉ mà shop đã cấu hình) ([9. API lấy danh sách địa chỉ lấy hàng | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-pick-addresses#:~:text=%7B%20,0987654321)).  
- **Lưu ý:** Dùng mã `pick_address_id` này thay vì phải nhập lại địa chỉ thủ công. Nếu API nhận thành công, `success` luôn `true` (có thể `data` rỗng nếu chưa có địa chỉ nào).  

## API lấy danh sách địa chỉ cấp 4
- **Phương thức:** GET  
- **Đường dẫn:** `/services/address/getAddressLevel4` ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=Endpoint)).  
- **Mô tả:** Với một địa chỉ cụ thể (đường/phường kèm tỉnh, quận), API trả về danh sách các địa chỉ cấp 4 (ví dụ: tên toà nhà, xóm, ngõ,…) trong khu vực đó. Đối tác cần dùng khi đăng đơn đối với một số địa chỉ bắt buộc phải nhập địa chỉ cấp 4.  

- **Yêu cầu (Request):**  
  - Header: `Token`, `X-Client-Source` như trên ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=Headers)).  
  - Tham số truy vấn:  
    - `province` (String, **bắt buộc**) – tên tỉnh/thành phố (ví dụ `"Hà Nội"`) ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,sa%CC%81ch%20%C4%91i%CC%A3a%20chi%CC%89%20c%E1%BA%A5p%204)).  
    - `district` (String, **bắt buộc**) – tên quận/huyện (ví dụ `"Quận Ba Đình"`) ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,sa%CC%81ch%20%C4%91i%CC%A3a%20chi%CC%89%20c%E1%BA%A5p%204)).  
    - `ward_street` (String, **bắt buộc**) – tên đường/phường (ví dụ `"Đội Cấn"`) ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=district%20yes%20String%20,sa%CC%81ch%20%C4%91i%CC%A3a%20chi%CC%89%20c%E1%BA%A5p%204)).  
    - `address` (String, không bắt buộc) – thông tin địa chỉ chi tiết cần tìm (ví dụ số nhà hoặc tên khu vực). Nếu có trường `address`, GHTK tự suy ra thông tin tỉnh/quận theo nội dung đó và có thể bỏ qua 3 tham số trên ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=L%C6%B0u%20%C3%BD%3A%20Trong%20tr%C6%B0%E1%BB%9Dng%20h%E1%BB%A3p,b%E1%BA%AFt%20bu%E1%BB%99c%20ph%E1%BA%A3i%20g%E1%BB%ADi%20l%C3%AAn)).  
  - **Ví dụ Request:**  
    ```
    GET /services/address/getAddressLevel4?province=h%C3%A0%20n%E1%BB%99i&district=ba%20%C4%91%C3%ACnh&ward_street=%C4%91%E1%BB%99i%20c%E1%BA%A5n&address=20
    ```  
    Mã hóa URL của Việt nam không dấu. Ví dụ trên sẽ lấy địa chỉ cấp 4 cho đường “Đội Cấn”, Quận Ba Đình, Hà Nội ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=GET%20%2Fservices%2Faddress%2FgetAddressLevel4%3Fprovince%3Dh,Source%3A%20%7BPARTNER_CODE)) ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,sa%CC%81ch%20%C4%91i%CC%A3a%20chi%CC%89%20c%E1%BA%A5p%204)).  

- **Phản hồi (Response):**  
  - **Ví dụ Response:**  
    ```json
    {
      "success": true,
      "data": [
        "IIG - 75 Giang Văn Minh",
        "Vinapaco Building - 142 Đội Cấn",
        "THCS Thống Nhất",
        "Ngõ 47 Đội Cấn",
        "Ngõ 46 Đội Cấn"
      ]
    }
    ```  
    Ở ví dụ, `data` là mảng các địa chỉ cấp 4 (có thể là tên toà nhà, ngõ) phù hợp với thông tin nhập ([10. API lấ́y danh sách địa chỉ cấp 4 | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-specific-addresses#:~:text=%7B%20,)).  
  - Nếu không tìm thấy kết quả, `data` sẽ trả mảng trống.  
- **Lưu ý:** `success` luôn trả về true nếu API gọi thành công (có thể không có phần tử nào). Đối tác nên sử dụng API này để xác định địa chỉ cấp 4 chính xác, tránh nhập thủ công.

# Webhook

- **Phương thức:** POST từ hệ thống GHTK sang máy chủ đối tác.  
- **Mô tả:** GHTK gửi webhook để thông báo cập nhật trạng thái đơn hàng đến hệ thống của đối tác. Khi trạng thái đơn (ví dụ “đã giao”, “giao một phần”,…) thay đổi, GHTK sẽ thực hiện HTTP POST gửi dữ liệu đến URL callback đối tác đã đăng ký (VD: `https://doitac.example.com/updateShipment?hash=XXX`) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=Gi%E1%BA%A3%20s%E1%BB%AD%20callback%20link%20c%E1%BB%A7a,%C4%91%E1%BB%91i%20t%C3%A1c%20l%C3%A0)). Dữ liệu được gửi dưới dạng form-urlencoded (hoặc JSON nội dung tương đương).  

- **Dữ liệu gửi cho đối tác:** JSON mẫu và mã hoá form như sau:  
  ```json
  {
    "partner_id": "1234567",
    "label_id": "S1.A1.17373471",
    "status_id": 5,
    "action_time": "2016-11-02T12:18:39+07:00",
    "reason_code": "",
    "reason": "",
    "weight": 2.4,
    "fee": 15000,
    "pick_money": 100000,
    "return_part_package": 0
  }
  ```  
  Hoặc dạng form-urlencoded tương đương:  
  ```
  label_id=S1.A1.17373471&partner_id=1234567&action_time=2016-11-02T12:18:39+07:00&status_id=5&reason_code=&reason=&weight=2.4&fee=15000&return_part_package=0
  ```  
  (Ví dụ trên là khi đơn mã “S1.A1.17373471” có `partner_id=1234567` được cập nhật trạng thái 5 “đã giao thành công”, với các thông tin về cân nặng, phí, tiền thu hộ, v.v.) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=https%3A%2F%2Fdoitac)) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=label_id%3DS1.A1.17373471%26partner_id%3D1234567%26action_time%3D2016)).

- **Các tham số (bắt buộc gửi kèm):** ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=Tham%20s%E1%BB%91%20Ki%E1%BB%83u%20d%E1%BB%AF%20li%E1%BB%87u,%C4%91%C6%A1n%20giao%20h%C3%A0ng%20m%E1%BB%99t%20ph%E1%BA%A7n))  
  - `label_id` (String) – mã vận đơn GHTK (mã trên hệ thống GHTK) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=Tham%20s%E1%BB%91%20Ki%E1%BB%83u%20d%E1%BB%AF%20li%E1%BB%87u,VN%C4%90)).  
  - `partner_id` (String) – mã đơn bên đối tác (nguyên `order.id` đối tác đã gửi) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=Tham%20s%E1%BB%91%20Ki%E1%BB%83u%20d%E1%BB%AF%20li%E1%BB%87u,kilogram)).  
  - `status_id` (Integer) – mã trạng thái mới của đơn (theo mã trạng thái GHTK) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=label_id%20String,kilogram)).  
  - `action_time` (String, ISO 8601) – thời gian cập nhật trạng thái (có múi giờ) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=partner_id%20String,VN%C4%90)).  
  - `reason_code` (String) – mã lý do (nếu có) cho trạng thái (ví dụ: lỗi khách hàng từ chối, thiếu địa chỉ, v.v.).  
  - `reason` (String) – mô tả chi tiết lý do thay đổi trạng thái (nếu có) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=status_id%20Integer,VN%C4%90)).  
  - `weight` (Float) – trọng lượng đơn hàng (kg) tại thời điểm giao hàng ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=action_time%20String%20%60ISO%208601%60%20,%C4%91%C6%A1n%20giao%20h%C3%A0ng%20m%E1%BB%99t%20ph%E1%BA%A7n)).  
  - `fee` (Integer) – phí ship đã áp dụng (VNĐ) ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=reason_code%20String,%C4%91%C6%A1n%20giao%20h%C3%A0ng%20m%E1%BB%99t%20ph%E1%BA%A7n)).  
  - `return_part_package` (Integer) – nếu bằng `1` nghĩa là đơn giao hàng một phần (có trả lại 1 phần), `0` nếu không ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=weight%20Float,%C4%91%C6%A1n%20giao%20h%C3%A0ng%20m%E1%BB%99t%20ph%E1%BA%A7n)).  

- **Xử lý response:** Đối tác phải trả về HTTP 200 nếu nhận webhook thành công ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=GHTK%20s%E1%BB%AD%20d%E1%BB%A5ng%20response%20HTTP,t%C3%A1c%20th%C3%A0nh%20c%C3%B4ng%20hay%20kh%C3%B4ng)). Nếu mã khác 200 hoặc không trả về, GHTK sẽ gửi lại vài lần nữa ([8. Webhook | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/webhook#:~:text=warning)). Không có JSON trong response; GHTK chỉ kiểm tra mã HTTP.  

# Kho và Sản phẩm (Warehouse & Product)

## API lấy danh sách thông tin sản phẩm
- **Phương thức:** GET  
- **Đường dẫn:** `/services/kho-hang/thong-tin-san-pham?term={term}` ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=Endpoint)).  
- **Mô tả:** Tìm kiếm và trả về danh sách sản phẩm mà shop đã tạo/đăng lên GHTK. Đối tác sử dụng mã `product_code` từ kết quả này khi đăng đơn (đối với các luồng yêu cầu mã sản phẩm sẵn). Nếu không tìm thấy sản phẩm khớp, `data` trả về mảng rỗng và đối tác có thể gửi `product_code: ""` khi đăng đơn để GHTK tạo mã mới ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=warning)).  

- **Yêu cầu (Request):**  
  - Header: `Token`, `X-Client-Source` như trên ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=Headers)).  
  - Tham số truy vấn:
    - `term` (String, **bắt buộc**) – từ khoá tên sản phẩm cần tìm ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,ph%E1%BA%A9m%20c%E1%BA%A7n%20l%E1%BA%A5y%20th%C3%B4ng%20tin)). Ví dụ: `term=laptop`.  
  - **Ví dụ Request:**  
    ```
    GET /services/kho-hang/thong-tin-san-pham?term=laptop HTTP/1.1
    Token: {API_TOKEN}
    X-Client-Source: {PARTNER_CODE}
    ```  
- **Phản hồi (Response):**  
  - **Trường trả về (`data`):** Mảng các sản phẩm tìm được. Mỗi phần tử có:  
    - `full_name` (String) – tên đầy đủ sản phẩm.  
    - `product_code` (String) – mã sản phẩm của GHTK.  
    - `weigh` (Number) – khối lượng tham chiếu (kg) của sản phẩm.  
    - `cost` (Integer) – giá trị (VNĐ) đã khai báo khi tạo sản phẩm.  
  - **Ví dụ nếu có sản phẩm:** (shop đã tạo sản phẩm từ trước)  
    ```json
    {
      "success": true,
      "data": [
        {
          "full_name": "Laptop Asus",
          "product_code": "23304A3MHLMVMXX625",
          "weigh": 2,
          "cost": 8000000
        },
        {
          "full_name": "Laptop Dell",
          "product_code": "23304A3MHLMVMXX888",
          "weigh": 2.5,
          "cost": 12000000
        }
      ]
    }
    ``` ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=TH1%20%3A%20Shop%20%C4%91%C3%A3%20t%E1%BB%ABng,ph%E1%BA%A9m%20tr%C3%AAn%20h%E1%BB%87%20th%E1%BB%91ng%20GHTK)).  
  - **Ví dụ nếu không có sản phẩm:**  
    ```json
    {
      "success": true,
      "data": []
    }
    ``` ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=TH2%20%3A%20S%E1%BA%A3n%20ph%E1%BA%A9m%20ch%C6%B0a,%C4%91%C4%83ng%20l%C3%AAn%20h%E1%BB%87%20th%E1%BB%91ng%20GHTK)).  
  - **Lưu ý:** Trong trường hợp `data` rỗng, khi đăng đơn đối tác gửi `product_code: ""` và GHTK sẽ tự tạo mã sản phẩm mới cho đơn đó ([11. API lấy danh sách thông tin sản phẩm | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-get-product#:~:text=warning)).  

# B2C (Thương mại điện tử)

B2C (Business-to-Consumer) cho phép sàn TMĐT quản lý nhiều shop con. Các API sau dành cho kịch bản B2C.

## API tạo tài khoản (Shop con)
- **Phương thức:** POST  
- **Đường dẫn:** `/services/shops/add` ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=)).  
- **Mô tả:** Tạo tài khoản shop (đại diện khách hàng) trên GHTK qua API. Dùng khi shop con chưa có tài khoản.  

- **Yêu cầu (Request):**  
  - Header: `Token` (token của tài khoản B2C hoặc chủ cửa hàng) và `Content-Type: application/json` ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=POST%20https%3A%2F%2Fservices.giaohangtietkiem.vn%2Fservices%2Fshops%2Fadd%20HTTP%2F1.1%20Token%3A%20APITokenSample,urlencoded)).  
  - Body (JSON) với các trường:
    - `name` (String, **bắt buộc**) – tên hiển thị của cửa hàng/shop con ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,s%E1%BB%91%20nh%C3%A0%2C%20ng%C3%B5%2C%20%C4%91%C6%B0%E1%BB%9Dng%2C%20ph%C6%B0%E1%BB%9Dng%2C%E2%80%A6)).  
    - `first_address` (String, **bắt buộc**) – địa chỉ chi tiết shop (số nhà, đường) ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,s%E1%BB%91%20nh%C3%A0%2C%20ng%C3%B5%2C%20%C4%91%C6%B0%E1%BB%9Dng%2C%20ph%C6%B0%E1%BB%9Dng%2C%E2%80%A6)).  
    - `province` (String, **bắt buộc**) – tỉnh/thành (ví dụ “Hà Nội”) ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=province%20yes%20T%C3%AAn%20t%E1%BB%89nh%2Fth%C3%A0nh%20ph%E1%BB%91%2C,yes%20email%20c%E1%BB%A7a%20t%C3%A0i%20kho%E1%BA%A3n)).  
    - `district` (String, **bắt buộc**) – quận/huyện (ví dụ “Bắc Từ Liêm”) ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=province%20yes%20T%C3%AAn%20t%E1%BB%89nh%2Fth%C3%A0nh%20ph%E1%BB%91%2C,yes%20email%20c%E1%BB%A7a%20t%C3%A0i%20kho%E1%BA%A3n)).  
    - `tel` (String, **bắt buộc**) – số điện thoại liên hệ của shop ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=province%20yes%20T%C3%AAn%20t%E1%BB%89nh%2Fth%C3%A0nh%20ph%E1%BB%91%2C,yes%20email%20c%E1%BB%A7a%20t%C3%A0i%20kho%E1%BA%A3n)).  
    - `email` (String, **bắt buộc**) – email dùng cho tài khoản shop ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=province%20yes%20T%C3%AAn%20t%E1%BB%89nh%2Fth%C3%A0nh%20ph%E1%BB%91%2C,yes%20email%20c%E1%BB%A7a%20t%C3%A0i%20kho%E1%BA%A3n)).  
  - **Ví dụ Request JSON:**  
    ```json
    {
      "name": "shop test",
      "first_address": "ngõ 2, Phan Bá Vành, Cầu Diễn",
      "province": "Hà Nội",
      "district": "Bắc Từ Liêm",
      "tel": "***********",
      "email": "<EMAIL>"
    }
    ```  
  - (Có thể gửi dưới dạng form-urlencoded như ví dụ gốc, hoặc JSON như trên) ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=)) ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=Tham%20s%E1%BB%91%20B%E1%BA%AFt%20bu%E1%BB%99c%20M%C3%B4,s%E1%BB%91%20nh%C3%A0%2C%20ng%C3%B5%2C%20%C4%91%C6%B0%E1%BB%9Dng%2C%20ph%C6%B0%E1%BB%9Dng%2C%E2%80%A6)).

- **Phản hồi (Response):**  
  - **Thành công:**  
    ```json
    {
      "success": true,
      "message": "Thành công",
      "data": {
        "code": "S55163",
        "token": "93A7392A44c99e7Ca395eef1321D03731B844111"
      }
    }
    ``` ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=%7B%20,%7D)).  
    - `data.code` là mã shop mới trên GHTK.  
    - `data.token` là token API của shop đó (sẽ sử dụng cho các API khác).  
  - **Lỗi:**  
    - Nếu email hoặc số điện thoại đã tồn tại:  
      ```json
      {
        "success": false,
        "message": "Email shop này đã tồn tại",
        "data": null
      }
      ``` ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=,d%E1%BB%A5ng%20cho%20t%C3%A0i%20kho%E1%BA%A3n%20kh%C3%A1c)).  
    - Nếu tài khoản B2C không có quyền tạo:  
      ```json
      {
        "success": false,
        "message": "Tài khoản của bạn không có quyền tạo tài khoản mới trên hệ thống GHTK",
        "data": null
      }
      ``` (thông báo ví dụ).  
- **Lưu ý:** Sau khi tạo, shop con có thể đăng nhập bằng email và dùng token trả về để sử dụng các API GHTK như shop thông thường ([13. Tạo tài khoản | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-account#:~:text=th%C3%B4ng%20tin)).  

## API lấy token tài khoản đã tồn tại
- **Phương thức:** POST  
- **Đường dẫn:** `/services/shops/token` ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=)).  
- **Mô tả:** Đối với shop con đã đăng ký trước đó, API này lấy token API cho tài khoản bằng email và mật khẩu đã đăng ký. Dùng khi B2C không muốn tạo lại tài khoản mới nhưng cần lấy token.  

- **Yêu cầu (Request):**  
  - Header: `Token` của chủ hệ thống B2C (đảm bảo tài khoản này có quyền dùng API) và `Content-Type: application/json` ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=POST%20https%3A%2F%2Fservices,Type%3A%20application%2Fjson)).  
  - Body (JSON) với:  
    - `email` (String, **bắt buộc**) – email tài khoản shop đã đăng ký ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=)).  
    - `password` (String, **bắt buộc**) – mật khẩu xác thực của tài khoản đó ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=)).  
  - **Ví dụ Request JSON:**  
    ```json
    {
      "email": "<EMAIL>",
      "password": "1S@fF#K2"
    }
    ```  
- **Phản hồi (Response):**  
  - **Thành công:** Trả về token của shop:  
    ```json
    {
      "success": true,
      "message": "Thành công",
      "data": {
        "code": "S19159",
        "token": "5568B780966147C0764EDD207af0233516E4c683"
      }
    }
    ``` ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=%7B%20,%7D)).  
    - `data.code` là mã cửa hàng (ví dụ S19159), `data.token` là token tương ứng.  
  - **Lỗi:**  
    - Nếu email/mật khẩu sai:  
      ```json
      {
        "success": false,
        "message": "Dữ liệu đăng nhập không hợp lệ"
      }
      ``` ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=,c%E1%BA%A7n%20l%E1%BA%A5y%20token%20kh%C3%B4ng%20%C4%91%C3%BAng)).  
    - Nếu tài khoản B2C không có quyền (gửi token của shop con khác):  
      ```json
      {
        "success": false,
        "message": "Tài khoản của bạn không có quyền sử dụng API này"
      }
      ``` ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=,token%20c%E1%BB%A7a%20t%C3%A0i%20kho%E1%BA%A3n%20kh%C3%A1c)).  
- **Lưu ý:** Chỉ tài khoản B2C hoặc tài khoản cao cấp mới được phép lấy token của tài khoản khác ([14. Tài khoản đã đăng ký trước | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-account-created-before#:~:text=)).

## API đăng đơn (B2C)
- **Phương thức:** POST (như API đăng đơn thường)  
- **Đường dẫn:** `/services/shipment/order` (giống API đăng đơn hàng ở phần E-Logistic) ([15. Đăng đơn | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-order#:~:text=POST%20%2Fservices%2Fshipment%2Forder%20HTTP%2F1.1%20Token%3A%20APITokenSample,Type%3A%20application%2Fjson)).  
- **Mô tả:** Tương tự API đăng đơn thông thường, nhưng khi đăng đơn cho shop con, cần thêm header `X-Refer-Token` chứa token của tài khoản B2C (shop sàn). Cụ thể, nếu shop con **HappyShop** đăng đơn nhưng server thực hiện từ tài khoản **SmileB2C**, thì thêm `X-Refer-Token: B2CToken` của SmileB2C vào header ([15. Đăng đơn | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-order#:~:text=SmileB2C%20c%C3%B3%20token%20l%C3%A0%20B2CToken)).  

- **Yêu cầu (Request):**  
  - Header:
    - `Token: {API_TOKEN}` – token của shop con (ví dụ HappyShop) ([15. Đăng đơn | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-order#:~:text=POST%20%2Fservices%2Fshipment%2Forder%20HTTP%2F1.1%20Token%3A%20APITokenSample,Type%3A%20application%2Fjson)).  
    - `X-Refer-Token: {TOKEN_B2C}` – token của tài khoản B2C (SmileB2C) ([15. Đăng đơn | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-order#:~:text=POST%20%2Fservices%2Fshipment%2Forder%20HTTP%2F1.1%20Token%3A%20APITokenSample,Type%3A%20application%2Fjson)).  
    - `Content-Type: application/json`.  
  - Body và tham số giống hệt API Đăng đơn (E-Logistic) phía trên, bao gồm `products`, `order`, v.v. (đã mô tả chi tiết ở phần trên).  
  - **Ví dụ Header (trích):**  
    ```
    POST /services/shipment/order HTTP/1.1
    Token: APITokenSample-ca441e70288cB0515F310742
    X-Refer-Token: B2CToken-hlsheiwquhrksadlfkjahsdfjaaljh
    Content-Type: application/json
    ```  
    (Token đầu là của shop con, `X-Refer-Token` là token B2C) ([15. Đăng đơn | Giao hàng tiết kiệm OpenApi](https://api.ghtk.vn/docs/submit-order/api-b2c-create-order#:~:text=POST%20%2Fservices%2Fshipment%2Forder%20HTTP%2F1.1%20Token%3A%20APITokenSample,Type%3A%20application%2Fjson)).  

- **Phản hồi (Response):** Giống API Đăng đơn hàng (E-Logistic). Khi thành công, đơn hàng sẽ được lưu dưới tên shop con HappyShop, nhưng GHTK cũng ghi nhận B2C ở header để đối soát.  
- **Lưu ý:** Cần sử dụng đúng header `X-Refer-Token`. Nếu thiếu, GHTK sẽ không biết đơn thuộc quyền sở hữu của B2C nào. 

