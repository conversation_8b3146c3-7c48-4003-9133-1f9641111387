<PERSON><PERSON><PERSON> tốt, mình sẽ tiến hành nghiên cứu toàn bộ API từ tài liệu https://developers.ahamove.com/docs/api-reference/, ghi rõ từng API, tham số, object liên quan và các lưu ý cần thiết. Mình sẽ trình bày theo dạng file Markdown (.md) với cấu trúc rõ ràng, chia từng mục tương tự như trong tài liệu gốc.

Mình sẽ cập nhật cho bạn ngay sau khi hoàn thành!

# API Ahamove (Tài liệu tham khảo)

Tài liệu này tổng hợp chi tiết các API của Ahamove (phiên bản 3.0.0). Mỗi API bao gồm phương thức và endpoint, mô tả chức năng, các tham số yêu cầu/không yêu cầu với kiểu dữ liệu và mô tả, cấu trúc đối tượng phản hồi (kiểu dữ liệu và ý nghĩa của từng trường), cùng các lưu ý quan trọng (xác thực, giới hạn, lỗi thường gặp, v.v.). Nội dung được chia thành các nhóm chính tương ứng với tài liệu gốc:

- **API Tài khoản (Account APIs)**: Đăng ký, xác thực, quản lý tài khoản con, cập nhật thông tin người dùng.
- **API Dữ liệu gốc (Master Data APIs)**: Lấy thông tin danh sách thành phố, dịch vụ, yêu cầu đặc biệt.
- **API Đơn hàng (Order APIs)**: Xem mô hình đơn hàng, ước tính phí, tạo/hủy/cập nhật đơn, lấy thông tin đơn, tracking, v.v.
- **Webhook/Callback**: Cơ chế cấu hình webhook để nhận sự kiện cập nhật đơn hàng.

## API Tài khoản (Account APIs)

### Đăng ký tài khoản (Register Account)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/accounts`
- **Mô tả:** Tạo tài khoản đối tác mới. Sau khi đăng ký thành công, Ahamove trả về cặp `token` và `refresh_token` để sử dụng tiếp. Phải cung cấp `api_key` do Ahamove cấp để xác thực.
- **Tham số yêu cầu:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                            |
  |-------------|--------|----------|----------------------------------|
  | `api_key`   | String | Có       | API key do Ahamove cấp sau khi đăng ký tích hợp ([Register Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/register-account#:~:text=Parameter%20Type%20Required%20Description%20api_key,after%20Partner%20registered%20for%20integration)). |
  | `mobile`    | String | Có       | Số điện thoại đăng ký tài khoản.  |
  | `name`      | String | Có       | Tên của đối tác.                |
  | `address`   | String | Có       | Địa chỉ của đối tác.            |

- **Cấu trúc phản hồi:** Trả về JSON chứa `token` và `refresh_token` (cả hai là chuỗi). Ví dụ: 
  ```json
  {
    "token": "eyJ0eXAiOiJKV1QiL...",
    "refresh_token": "0c42af2105249b126b80862e96b3b055"
  }
  ```
  (các giá trị minh họa) ([Register Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/register-account#:~:text=%7B%20,)).
- **Lưu ý:**  
  - Tham số `api_key` là bắt buộc để xác thực yêu cầu.  
  - Token trả về có hạn dùng (được mã hóa JWT).  
  - **Lỗi thường gặp:** 400 (`INVALID_DATA`: thiếu hoặc sai dữ liệu), 404 (`PARTNER_NOT_FOUND`: API key không hợp lệ), 409 (`ACCOUNT_EXISTED`: tài khoản đã tồn tại) ([Register Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/register-account#:~:text=Code%20Text%20Description%20,Try%20again%20later)).  

### Xác thực token (Authenticate token)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/accounts/token`
- **Mô tả:** Tạo mới token (đăng nhập) cho tài khoản đã có khi token cũ hết hạn.  
- **Tham số yêu cầu:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                            |
  |-------------|--------|----------|----------------------------------|
  | `api_key`   | String | Có       | API key đã được cấp (giống `api_key` ở trên) ([Authenticate token | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/authenticate-token#:~:text=Parameter%20Type%20Required%20Description%20api_key,number%20registered%20to%20the%20account)). |
  | `mobile`    | String | Có       | Số điện thoại của tài khoản (phải cùng tài khoản đã đăng ký) ([Authenticate token | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/authenticate-token#:~:text=Parameter%20Type%20Required%20Description%20api_key,number%20registered%20to%20the%20account)). |

- **Cấu trúc phản hồi:** Tương tự như **Register Account**, trả về `token` và `refresh_token` ([Authenticate token | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/authenticate-token#:~:text=%7B%20,)).  
- **Lưu ý:**  
  - Yêu cầu `api_key` và `mobile` phải hợp lệ.  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: tài khoản hiện tại không thuộc partner), 404 (`USER_NOT_FOUND` hoặc `PARTNER_NOT_FOUND`: không tìm thấy), 406 (`DEACTIVATED_ACCOUNT`: tài khoản đã bị tạm ngưng) ([Authenticate token | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/authenticate-token#:~:text=Errors)).

### Thêm tài khoản con (Add Child Account)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/accounts/childs`
- **Mô tả:** Thêm một tài khoản con (số điện thoại) vào tài khoản cha. Sau khi gọi API, Ahamove gửi mã xác thực về số điện thoại con qua SMS để xác nhận.  
- **Xác thực:** Cần header `Authorization: Bearer <token>` với token của tài khoản cha.  
- **Tham số yêu cầu:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                            |
  |-------------|--------|----------|----------------------------------|
  | `child_id`  | String | Có       | Số điện thoại của tài khoản con cần thêm ([Add Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/add-child-account#:~:text=Query%20Parameters)). |

- **Cấu trúc phản hồi:** Trả về đối tượng rỗng `{}` nếu thành công ([Add Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/add-child-account#:~:text=)).  
- **Lưu ý:**  
  - Token trong header phải là token của tài khoản cha (được xác thực bởi partner) ([Add Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/add-child-account#:~:text=Parameter%20Value%20Required%20Description%20Content,Token%20of%20the%20parent%20account)).  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: tài khoản cha không liên kết với partner hoặc đã là con của partner khác), 404 (`NOT_FOUND`), 406 (`ACCOUNT_NOT_SAME_PARTNER`: tài khoản con không cùng partner), 409 (`PARENT_ACCOUNT_EXISTED`: tài khoản con đã có cha) ([Add Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/add-child-account#:~:text=Code%20Text%20Description%20%60401%20%60%60NOT_AUTHORIZED%60,already%20has%20a%20parent%20account)).

### Kích hoạt tài khoản con (Activate Child Account)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/accounts/childs/<child_id>/activate`
- **Mô tả:** Xác nhận liên kết tài khoản con với tài khoản cha bằng mã kích hoạt đã gửi SMS.  
- **Xác thực:** Header `Authorization: Bearer <token>` với token của tài khoản cha.  
- **Tham số yêu cầu:**

  | Tên tham số       | Kiểu   | Bắt buộc | Mô tả                                          |
  |-------------------|--------|----------|------------------------------------------------|
  | `child_id`        | String | Có       | Số điện thoại của tài khoản con (trùng với `<child_id>` trong URL) ([Activate Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/activate-child-account#:~:text=Parameter%20Type%20Required%20Description%20child_id,the%20child%20account%20for%20verification)). |
  | `activation_code` | String | Có       | Mã kích hoạt nhận được qua SMS ([Activate Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/activate-child-account#:~:text=Parameter%20Type%20Required%20Description%20child_id,the%20child%20account%20for%20verification)).  |

- **Cấu trúc phản hồi:** Trả về đối tượng JSON chi tiết tài khoản con sau khi kích hoạt, ví dụ:
  ```json
  {
    "_id": "849xxxxxxxx",
    "name": "Nguyễn Văn A",
    "partner": "ahamove",
    "parent_id": "***********",
    ...
  }
  ```
  (bao gồm các trường `_id`, `name`, `partner`, `parent_id`, v.v.) ([Activate Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/activate-child-account#:~:text=%7B%20,...)).  
- **Lưu ý:**  
  - Phải cung cấp mã chính xác.  
  - **Lỗi thường gặp:** 404 (`NOT_FOUND`: token không tìm thấy hoặc `child_id` không tồn tại), 401 (`NOT_AUTHORIZED`: cha không liên kết hoặc đã là con), 406 (`INVALID_ACTIVATION_CODE`: mã kích hoạt sai), 409 (`PARENT_ACCOUNT_EXISTED`: tài khoản con đã có cha) ([Activate Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/activate-child-account#:~:text=Code%20Text%20Description%20%60404%20%60%60NOT_FOUND%60,Try%20again%20later)).

### Xóa tài khoản con (Remove Child Account)

- **Phương thức / Endpoint:** `DELETE https://partner-apistg.ahamove.com/v3/accounts/childs/<child_id>`
- **Mô tả:** Hủy liên kết tài khoản con khỏi tài khoản cha. Yêu cầu tài khoản con không có đơn đang xử lý và đã thanh toán đủ.  
- **Xác thực:** Header `Authorization: Bearer <token>` với token của tài khoản cha.  
- **Tham số yêu cầu:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                            |
  |-------------|--------|----------|----------------------------------|
  | `child_id`  | String | Có       | Số điện thoại của tài khoản con cần xóa ([Remove Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/remove-child-account#:~:text=Query%20Parameters)). |

- **Cấu trúc phản hồi:** Trả về JSON `{"status": "success"}` nếu thành công ([Remove Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/remove-child-account#:~:text=)).  
- **Lưu ý:**  
  - Tài khoản con phải không có đơn chưa hoàn thành và không nợ phí.  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: cha không liên kết/đã là con), 402 (`NOT_ALLOWED`: con đang có đơn/dư nợ), 404 (`NOT_FOUND`: token hoặc `child_id` không tồn tại), 406 (`INVALID_PHONE_NUMBER`: số điện thoại không hợp lệ) ([Remove Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/remove-child-account#:~:text=Code%20Text%20Description%20%60401%20%60%60NOT_AUTHORIZED%60,Try%20again%20later)).

### Danh sách tài khoản con (Get Child Account List)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/accounts/childs`
- **Mô tả:** Lấy danh sách tất cả tài khoản con liên kết với tài khoản cha (user_id được xác định qua token).  
- **Xác thực:** Header `Authorization: Bearer <token>` với token của tài khoản cha.  
- **Tham số yêu cầu:** Không có tham số bắt buộc (sử dụng token để xác định tài khoản cha).  
- **Cấu trúc phản hồi:** Mảng JSON các đối tượng tài khoản con, mỗi đối tượng có các trường như:

  | Trường           | Kiểu    | Mô tả                                                |
  |------------------|---------|------------------------------------------------------|
  | `_id`            | String  | ID tài khoản con (số điện thoại) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=%5B%20%7B%20,VN)).     |
  | `name`           | String  | Tên của tài khoản con.                               |
  | `partner`        | String  | Mã partner (api key) liên kết.                       |
  | `email`          | String  | Email (nếu có).                                      |
  | `referral_code`  | String  | Mã giới thiệu (thường là số điện thoại) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=%5B%20%7B%20,VN)). |
  | `country_code`   | String  | Mã quốc gia (ví dụ `VN`) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=%5B%20%7B%20,VN)).            |
  | `currency`       | String  | Mã tiền tệ (ISO 4217), ví dụ `VND` ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=%5B%20%7B%20,VN)).   |
  | `account_status` | String  | Trạng thái tài khoản (`ACTIVATED`, `DEACTIVATED`...) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=,SGN)). |
  | `create_time`    | Number  | Thời gian tạo (epoch timestamp) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=,SGN)).      |
  | `parent_id`      | String  | Số điện thoại của tài khoản cha.                      |
  | `city_id`        | Array   | Danh sách mã thành phố được phép (mặc định theo cha). |
  | ...              |         | (có thể có các trường phụ khác như `home` rỗng)       |

- **Lưu ý:**  
  - Nếu không có tài khoản con nào, trả về mảng rỗng.  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: token không hợp lệ) ([Get Child Account List | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/get-child-account-list#:~:text=Errors)).

### Cập nhật thông tin tài khoản (Update User Profile)

- **Phương thức / Endpoint:** `PUT https://partner-apistg.ahamove.com/v3/accounts`
- **Mô tả:** Cập nhật thông tin cá nhân của tài khoản (tên, email).  
- **Xác thực:** Header `Authorization: Bearer <token>` với token của tài khoản.  
- **Tham số:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                 |
  |-------------|--------|----------|-----------------------|
  | `name`      | String | Không    | Tên người dùng mới (nếu muốn cập nhật).  |
  | `email`     | String | Không    | Email mới (nếu muốn cập nhật).            |

- **Cấu trúc phản hồi:** Đối tượng JSON rỗng `{}` nếu thành công ([Update User Profile | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/update-user-profile#:~:text=Response)).  
- **Lưu ý:**  
  - Chỉ cập nhật được nếu token hợp lệ và không vi phạm định dạng.  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: token không hợp lệ), 406 (`Not Acceptable`: định dạng email sai), 409 (`EXISTED_EMAIL`: email đã tồn tại) ([Update User Profile | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/update-user-profile#:~:text=Code%20Text%20Description%20,Try%20again%20later)).

## API Dữ liệu gốc (Master Data APIs)

Các API nhóm này giúp lấy danh sách thông tin (thành phố, dịch vụ, yêu cầu đặc biệt) để hỗ trợ xây dựng đơn hàng.

### Lấy danh sách thành phố (Get Cities)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/cities?country_id=VN`
- **Mô tả:** Lấy danh sách các thành phố (với `city_id`) mà Ahamove đang hỗ trợ tại Việt Nam.  
- **Xác thực:** Header `Authorization: Bearer <token>` của tài khoản đã đăng nhập ([Get Cities | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-cities#:~:text=curl%20,token)).  
- **Tham số truy vấn:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả          |
  |-------------|--------|----------|----------------|
  | `country_id`| String | Có       | Mã quốc gia, thường là `VN` ([Get Cities | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-cities#:~:text=curl%20,token)). |

- **Cấu trúc phản hồi:** Mảng JSON các đối tượng thành phố, mỗi đối tượng có ít nhất:

  | Trường        | Kiểu   | Mô tả                              |
  |---------------|--------|------------------------------------|
  | `_id`         | String | Mã thành phố (`city_id`), ví dụ `"HAN"`.   |
  | `name`        | String | Tên thành phố (tiếng Anh).         |
  | `name_vi_vn`  | String | Tên thành phố (tiếng Việt) ([Get Cities | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-cities#:~:text=%5B%20%7B%20,...)). |
  | `country_id`  | String | Mã quốc gia (`VN` cấp từ tham số).  |
  | ...           | ...    | Các trường khác nếu có (ví dụ danh sách dịch vụ). |

  Ví dụ phản hồi:
  ```json
  [
    {
      "_id": "HAN",
      "name": "Ha Noi",
      "name_vi_vn": "Hà Nội",
      "country_id": "VN",
      ...
    },
    {
      "_id": "SGN",
      "name": "Ho Chi Minh City",
      "name_vi_vn": "TP Hồ Chí Minh",
      "country_id": "VN",
      ...
    }
  ]
  ```
  ([Get Cities | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-cities#:~:text=%5B%20%7B%20,...)).
- **Lưu ý:**  
  - Token trong header phải hợp lệ.  
  - Kết quả có thể kèm thêm thông tin phụ.  
  - **Lỗi thường gặp:** (thường ít) 401 (token sai), 500, 503.

### Lấy danh sách dịch vụ (Get List Of Service)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/services`
- **Mô tả:** Lấy danh sách mã dịch vụ (với thông tin phí và điều kiện) tại một thành phố hoặc theo tọa độ.  
- **Xác thực:** Header `Authorization: Bearer <token>` ([Get List Of Service | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-service#:~:text=curl%20,token%3E%27)).  
- **Tham số truy vấn:**

  | Tên tham số    | Kiểu   | Bắt buộc  | Mô tả                                                                 |
  |----------------|--------|-----------|-----------------------------------------------------------------------|
  | `city_id`      | String | Có (nếu không dùng `lat`/`lng`) | Mã thành phố (ví dụ `SGN`). Yêu cầu nếu không cung cấp `lat`/`lng`. |
  | `lat`          | String | Có (nếu không dùng `city_id`) | Vĩ độ. Yêu cầu nếu không có `city_id`.                                 |
  | `lng`          | String | Có (nếu không dùng `city_id`) | Kinh độ. Yêu cầu nếu không có `city_id`.                              |
  | `delivery_type`| String | Không    | Loại giao hàng (`INSTANT` cho giao ngay, `TRUCK` cho dịch vụ xe tải...). |

- **Cấu trúc phản hồi:** Mảng JSON các đối tượng dịch vụ với nhiều trường quan trọng như:

  | Trường                        | Kiểu    | Mô tả                                                                                     |
  |-------------------------------|---------|-------------------------------------------------------------------------------------------|
  | `_id`                         | String  | Mã dịch vụ (ví dụ `SGN-BIKE`).                                                            |
  | `city_id`                     | String  | Mã thành phố.                                                                             |
  | `delivery_type`               | String  | Loại giao hàng (`INSTANT`, `TRUCK`,...).                                                 |
  | `distance_fee`                | String  | Công thức hoặc biểu thức tính phí theo khoảng cách (ví dụ `(25000 if x <= 2 else ...)`).  |
  | `cod`                         | Number  | Mức COD tối đa (nếu dịch vụ hỗ trợ COD).                                                  |
  | `commission_type`             | String  | Loại hoa hồng (thường `PERCENTAGE`).                                                     |
  | `commission_value`            | Number  | Giá trị hoa hồng (theo % hoặc tiền).                                                     |
  | `currency`                    | String  | Mã tiền tệ (ISO) (ví dụ `VND`).                                                           |
  | `description_en_us`           | String  | Mô tả dịch vụ (Tiếng Anh).                                                               |
  | `description_vi_vn`           | String  | Mô tả dịch vụ (Tiếng Việt).                                                              |
  | `requests`                    | Array   | Danh sách **Request** (chi tiết yêu cầu đặc biệt) cho dịch vụ này, mỗi mục có:           |
  | &nbsp;&nbsp;`_id`             | String  | Mã yêu cầu đặc biệt (ví dụ `SGN-BIKE-TIP`).                                               |
  | &nbsp;&nbsp;`name`            | String  | Tên yêu cầu (ví dụ `"Hỗ trợ tài xế (PER_UNIT)"`).                                        |
  | &nbsp;&nbsp;`description_en_us`| String  | Mô tả (Anh).                                                                             |
  | &nbsp;&nbsp;`description_vi_vn`| String  | Mô tả (Việt).                                                                             |
  | &nbsp;&nbsp;`group_id`        | String  | Nhóm yêu cầu (ví dụ `TIP`, `BULKY`, ...).                                                 |
  | &nbsp;&nbsp;`price`           | Number  | Giá của yêu cầu.                                                                         |
  | &nbsp;&nbsp;`max_input`       | Number  | Số lượng tối đa (nếu có).                                                                |
  | ...                           |         | Có thể có các trường khác (ví dụ `device_types`, `enable`).                             |

  Ví dụ rút gọn (loại bỏ một số trường): 
  ```json
  [
    {
      "_id": "SGN-BIKE",
      "city_id": "SGN",
      "delivery_type": "INSTANT",
      "cod": 10000000,
      "commission_type": "PERCENTAGE",
      "commission_value": 0.212,
      "currency": "VND",
      "description_en_us": "Giao hàng nội thành trong 1 giờ",
      "description_vi_vn": "Lấy hàng ngay, giao hỏa tốc",
      "distance_fee": "(25000 if x <= 2 else ...)",
      "requests": [
        {
          "_id": "SGN-BIKE-TIP",
          "group_id": "TIP",
          "name": "Hỗ trợ tài xế (PER_UNIT)",
          "price": 5000,
          "description_en_us": "Support tip...",
          "description_vi_vn": "Hỗ trợ tài xế...",
          "max_input": 6
        }
      ]
    },
    ...
  ]
  ```
  (Ví dụ chi tiết trong tài liệu ([Get List Of Service | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-service#:~:text=)) ([Get List Of Service | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-service#:~:text=,n%C6%A1i%20ph%E1%BA%A3i%20t%E1%BB%91n%20ph%C3%AD%20gi%E1%BB%AF)).)
- **Lưu ý:**  
  - Cần header `Content-Type: application/json` và token hợp lệ ([Get List Of Service | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-service#:~:text=curl%20,token%3E%27)).  
  - Nếu không cung cấp `city_id`, phải có cả `lat` và `lng`.  
  - **Lỗi thường gặp:** 404 (`CITY_NOT_FOUND`: không tìm thấy thành phố), 406 (`INVALID_DELIVERY_TYPE`: loại giao không hợp lệ) ([Get List Of Service | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-service#:~:text=Code%20Text%20Description%20%60404%20%60%60CITY_NOT_FOUND%60,Try%20again%20later)).

### Lấy chi tiết dịch vụ (Get Service Details)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/services/<service_id>`
- **Mô tả:** Lấy chi tiết thông tin của một dịch vụ cụ thể (tương tự một phần tử từ kết quả Get List Of Service).  
- **Xác thực:** Header `Authorization: Bearer <token>`.  
- **Tham số truy vấn:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                            |
  |-------------|--------|----------|----------------------------------|
  | `service_id`| String | Có       | Mã dịch vụ Ahamove (ví dụ `SGN-BIKE`) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=%5B%20%7B%20%22_id%22%3A%20%22SGN,0.212)). |

- **Cấu trúc phản hồi:** Tương tự đối tượng trong **Get List Of Service**, bao gồm tất cả trường như `_id`, `city_id`, `delivery_type`, `cod`, `commission_type`, `commission_value`, `currency`, `description_en_us`, `description_vi_vn`, `distance_fee`, cũng như danh sách `requests` (các yêu cầu đặc biệt) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=%5B%20%7B%20%22_id%22%3A%20%22SGN,0.212)) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=,n%C6%A1i%20ph%E1%BA%A3i%20t%E1%BB%91n%20ph%C3%AD%20gi%E1%BB%AF)). Ví dụ trả về:
  ```json
  [
    {
      "_id": "SGN-BIKE",
      "city_id": "SGN",
      "delivery_type": "INSTANT",
      "cod": 10000000,
      "commission_type": "PERCENTAGE",
      "commission_value": 0.212,
      "currency": "VND",
      "description_en_us": "Giao hàng nội thành trong 1 giờ",
      "description_vi_vn": "Lấy hàng ngay, giao hỏa tốc",
      "distance_fee": "(25000 if x <= 2 else ...)",
      "requests": [
        {
          "_id": "SGN-BIKE-TIP",
          "group_id": "TIP",
          "name": "Hỗ trợ tài xế (PER_UNIT)",
          "price": 5000,
          "description_vi_vn": "Hãy chọn dịch vụ này...",
          ...
        }
      ]
    }
  ]
  ```
  (Ví dụ được trích xuất từ tài liệu ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=%5B%20%7B%20%22_id%22%3A%20%22SGN,0.212)) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=,n%C6%A1i%20ph%E1%BA%A3i%20t%E1%BB%91n%20ph%C3%AD%20gi%E1%BB%AF)).)
- **Lưu ý:**  
  - **Lỗi thường gặp:** 404 (`SERVICE_NOT_FOUND`: dịch vụ không tồn tại) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=Errors)).

### Yêu cầu đặc biệt (Special Requests)

- **Mô tả:** Không phải endpoint riêng biệt. Danh sách các **yêu cầu đặc biệt** (các bổ sung như tip, đông lạnh, v.v.) được chứa trong trường `requests` trả về của hai API trên. Để biết các loại yêu cầu, thực hiện:
  1. Gọi **Get List Of Service** hoặc **Get Service Details**.
  2. Xem trường `requests` trong kết quả trả về ([Get List Of Special Request | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-special-request#:~:text=How%20to%20get%20list%20of,special%20request)).  
- **Lưu ý:**  
  - Mỗi `request` có `group_id` (loại yêu cầu như TIP, BULKY, v.v.) và các trường phụ (như `num`, `tier_code`, `price`) tùy loại.  
  - Bảng dưới đây minh họa một số loại yêu cầu đặc biệt (theo **Group ID**):

    | Group ID    | Loại yêu cầu                 |
    |-------------|------------------------------|
    | `TIP`       | Tiền boa tài xế (PER_UNIT)   |
    | `BULKY`     | Hàng cồng kềnh (có phân tầng)|
    | `D2D`, `FRAGILE`, `SMS` | Yêu cầu có/không (BOOLEAN)  |
    | `INSURANCE` | Bảo hiểm COD                |
    | ...         | ...                          |

  - Ví dụ, yêu cầu `SGN-BIKE-TIP` có `group_id` = `TIP`, yêu cầu `SGN-BIKE-BULKY` có `group_id` = `BULKY` ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=,n%C6%A1i%20ph%E1%BA%A3i%20t%E1%BB%91n%20ph%C3%AD%20gi%E1%BB%AF)). Tài liệu cũng cung cấp mẫu các thông số `tier_code` cho nhóm `BULKY` ([Get List Of Special Request | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-list-of-special-request#:~:text=)).

## API Đơn hàng (Order APIs)

Trước khi xem chi tiết từng API, xem **Mô hình dữ liệu (Data Model)** của một đơn hàng để hiểu các trường dữ liệu.

### Mô hình đơn hàng (Data Model)

- **Mô tả:** Giải thích cấu trúc JSON của một đơn hàng hoàn chỉnh trong hệ thống Ahamove. (Dựa trên ví dụ và bảng trường dữ liệu từ tài liệu).  
- **Ví dụ JSON mẫu:** Một đơn hàng (ở trạng thái `CANCELLED`) có thể chứa:
  ```json
  {
    "_id": "QMB0HO",
    "status": "CANCELLED",
    "service_id": "SGN-TRICYCLE",
    "city_id": "SGN",
    "requests": [
      {"_id": "SGN-TRUCK-500-CART", "num": 2, "price": 100000},
      {"_id": "SGN-TRUCK-500-BOCXEP", "price": 50000},
      {"_id": "SGN-TRUCK-500-COD", "price": 0, "value": 300000},
      {"_id": "SGN-COFFEEHOUSE-COMBO-1", "num": 2, "item_price": 980000}
    ],
    "user_id": "84972709963",
    "user_name": "Thao Vy",
    "supplier_id": "84909561477",
    "supplier_name": "Hieu Nguyen",
    "partner": "foodbook",
    "order_time": **********,
    "create_time": **********.649361,
    "accept_time": **********.189912,
    "cancel_time": **********.374609,
    "cancel_comment": "Lịch chuyển nhà bị hoãn",
    "cancel_by_user": false,
    "currency": "VND",
    "stop_fee": 0,
    "request_fee": 0,
    "distance": 5.597,
    "distance_fee": 142358,
    "promo_code": "AHAMOVE",
    "discount": 0,
    "total_fee": 142358,
    "payment_method": "BALANCE",
    "user_bonus_account": 0,
    "user_main_account": 0,
    "total_pay": 142358,
    "supplier_main_account": 0,
    "supplier_bonus_account": 14235.8,
    "partner_fee": 20000,
    "partner_distance_fee": 10000,
    "partner_subsidy": 20000,
    "partner_discount": 10000,
    "partner_pay": 90000,
    "online_pay": 0,
    "rating_by_user": 4,
    "comment_by_user": "Good",
    "rating_by_supplier": 5,
    "comment_by_supplier": "Great customer",
    "rating_by_receiver": 4,
    "comment_by_receiver": "Good driver",
    "store_rating_by_user": 4,
    "store_comment_by_user": "Delicious food",
    "path": [
      {"lat": 10.7890462, "lng": 106.7763078, "address": "Địa chỉ A"},
      {
        "lat": 10.7640358, "lng": 106.7558794, "address": "Địa chỉ B",
        "mobile": "**********", "name": "Ngon", "cod": 500000,
        "remarks": "Call me", "require_pod": true, "require_verification": true,
        "complete_time": **********, "complete_lat": 10.7890462,
        "status": "COMPLETED", "pod_info": "*********"
      },
      {
        "lat": 10.7640412, "lng": 106.755910, "address": "Địa chỉ C",
        "mobile": "**********", "name": "Ngon", "cod": 500000,
        "remarks": "Call me", "status": "FAILED",
        "fail_time": **********, "fail_comment": "User does not show",
        "redelivery_note": {
          "from_time": 1532109600, "to_time": 1532111400,
          "address": "Địa chỉ giao lại", "lat": 10.782938, "lng": 106.704899
        }
      }
    ],
    "remarks": "Đến nơi gọi điện cho tôi",
    "remind": true,
    "assigned_by": "84908842285",
    "index": 1,
    "from_location": {"type": "Point", "coordinates": [106.7763078, 10.7890462]}
  }
  ```
- **Giải thích một số trường quan trọng:**

  | Trường               | Kiểu   | Mô tả                                                      |
  |----------------------|--------|------------------------------------------------------------|
  | `_id`                | String | Mã đơn hàng (Order ID).                                    |
  | `status`             | String | Trạng thái hiện tại (các giá trị: PENDING, ASSIGNING, ACCEPTED, PICKED_UP, COMPLETED, CANCELLED, FAILED, v.v.). |
  | `service_id`         | String | Mã dịch vụ AhaMove (ví dụ `SGN-BIKE`).                     |
  | `city_id`            | String | Mã thành phố.                                             |
  | `requests`           | Array  | Danh sách yêu cầu đặc biệt (mẫu như trên).                |
  | `user_id`, `user_name`       | String  | ID và tên người tạo đơn (tài khoản cha).               |
  | `supplier_id`, `supplier_name` | String  | ID và tên tài xế khi đã nhận đơn.                        |
  | `order_time`         | Number | Thời gian đặt (epoch, 0 nếu giao ngay).                   |
  | `create_time`        | Number | Thời gian tạo đơn (epoch).                                |
  | `accept_time`, `complete_time`, `cancel_time`, `fail_time` | Number | Thời gian các mốc: tài xế nhận, hoàn thành, hủy, thất bại. |
  | `cancel_by_user`     | Boolean| `true` nếu người dùng hủy, `false` nếu tài xế hủy.        |
  | `currency`           | String | Mã tiền tệ (e.g. `VND`).                                 |
  | Phần **Thanh toán**: |        |                                                            |
  | `distance`           | Number | Quãng đường (km) tính phí.                               |
  | `distance_fee`       | Number | Phí tính theo khoảng cách.                                |
  | `request_fee`        | Number | Tổng phí từ các `requests` (yêu cầu phụ).                 |
  | `stop_fee`           | Number | Phí giao nhiều điểm (stop-over fee) nếu có.             |
  | `discount`           | Number | Tổng giảm giá.                                            |
  | `total_fee`          | Number | Phí đơn (sum các phí trên) (chưa trừ giảm giá).          |
  | `user_bonus_account`, `user_main_account` | Number | Số tiền đã trừ từ tài khoản bonus và main (nếu có). |
  | `total_pay`          | Number | Tổng số tiền khách phải trả (bao gồm COD nếu có) ([Data Model | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/data-model#:~:text=,Request%20Fee%20%2B%20Stop)). |
  | **Thanh toán đối tác (food):** | | Nếu là đơn hàng đối tác (có `partner_fee`, `partner_discount`...). |
  | `partner_fee`, `partner_distance_fee`, `partner_subsidy`, `partner_discount`, `partner_pay` | Number | Các trường tính toán chi phí đối tác. |
  | **Đánh giá:** | |                                                            |
  | `rating_by_user`, `comment_by_user` | Number, String | Đánh giá và nhận xét của người gửi dành cho tài xế.      |
  | `rating_by_supplier`, `comment_by_supplier` | Number, String | Đánh giá và nhận xét của tài xế dành cho khách.      |
  | **Path (địa điểm):** |        | Danh sách các điểm giao: index 0 là điểm lấy hàng (Pickup), tiếp đến là các điểm giao (delivery). Mỗi mục có: |
  | `lat`, `lng`         | Number | Vĩ độ, kinh độ điểm.                                        |
  | `address`            | String | Địa chỉ (mẫu: `Số nhà, Đường, Phường, Quận, Tỉnh`).         |
  | `name`, `mobile`     | String | Tên và số điện thoại người nhận (không bắt buộc ở index 0).  |
  | `tracking_number`    | String | Mã đơn nội bộ của đối tác (nếu có).                         |
  | `cod`                | Number | Số tiền thu COD ở điểm giao (nếu có).                       |
  | `remarks`            | String | Ghi chú riêng tại mỗi điểm.                                 |
  | `status`             | String | Trạng thái tại điểm (nếu là delivery point).               |
  | `require_verification` | Boolean | Yêu cầu mã xác nhận (nếu có).                             |
  | `require_pod`        | Boolean | Yêu cầu biên bản giao hàng (nếu có).                       |
  | `complete_time`, `fail_time` | Number | Thời gian giao/xử lý thất bại tại điểm.                |
  | `redelivery_note`    | Object | Thông tin giao lại (nếu có) như `from_time`, `to_time`, `address`, `lat`, `lng`. |

(Một số trường trong ví dụ đã bị bớt cho dễ nhìn, xem nguyên gốc ở tài liệu mẫu [31–33].)

### Ước tính phí đơn hàng (Estimate Order Fee)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/orders/estimates`
- **Mô tả:** Ước tính phí đơn hàng theo nhiều dịch vụ. Yêu cầu cung cấp thông tin lộ trình và danh sách dịch vụ cần so sánh.  
- **Xác thực:** Header `Content-Type: application/json` và `Authorization: Bearer <token>` của người tạo đơn ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=Headers)).  
- **Thân (Body):** JSON với các trường chính:
  - `order_time` (Number): Thời gian giao (0 nếu giao ngay).  
  - `path` (Array): Danh sách điểm (ít nhất 2), tương tự định nghĩa ở trên ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%7B%20,Th%C3%A0nh%20Th%C3%A1i%2C%20Qu%E1%BA%ADn%2010)). Mỗi đối tượng gồm `lat`, `lng`, `address`, có thể thêm `name`, `mobile`, `cod`, `item_value`, `tracking_number`, `remarks`.  
  - `services` (Array): Danh sách dịch vụ để so sánh. Mỗi phần tử:
    - `_id` (String): Mã dịch vụ (vd `SGN-BIKE`) ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%22services%22%3A%20%5B%20%7B%20%22_id%22%3A%20%22SGN,TIP%22%2C%20%22num%22%3A%201)).
    - `requests` (Array): Các yêu cầu đặc biệt kèm theo, ví dụ `{"_id": "SGN-BIKE-TIP", "num": 1}` hoặc `{"_id": "SGN-BIKE-BULKY", "tier_code": "TIER_2"}` ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%22services%22%3A%20%5B%20%7B%20%22_id%22%3A%20%22SGN,BULKY)).
  - `payment_method` (String): Phương thức thanh toán (`CASH`, `BALANCE`, `CASH_BY_RECIPIENT`).  
  - `remarks` (String, Không bắt buộc): Ghi chú đơn hàng.  
  - `promo_code` (String, Không bắt buộc): Mã khuyến mãi, nếu có.  
  - `items` (Array, Không bắt buộc): Danh sách hàng hoá (có `_id`, `num`, `name`, `price`) nếu áp dụng ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=,GDB)).
  - Có thể có thêm (nếu có): `origin_id`, `partner`, ... tùy đối tác.

- **Ví dụ Body (rút gọn):**  ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%7B%20,Lan)) ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%22services%22%3A%20%5B%20%7B%20%22_id%22%3A%20%22SGN,BULKY))  
  ```json
  {
    "order_time": 0,
    "path": [
      {"lat": 10.76975346, "lng": 106.6636615, "address": "Địa chỉ A", "name": "Lan", "mobile": "84944309348"},
      {"lat": 10.8018493, "lng": 106.714466, "address": "Địa chỉ B", "name": "Anh", "mobile": "0912345678", "cod": 100000, "tracking_number": "ABCD1234"}
    ],
    "services": [
      {"_id": "SGN-BIKE", "requests": [{"_id": "SGN-BIKE-TIP", "num": 1}, {"_id": "SGN-BIKE-BULKY", "tier_code": "TIER_2"}]},
      {"_id": "SGN-ECO", "requests": []}
    ],
    "payment_method": "CASH",
    "remarks": "Ghi chú",
    "promo_code": "AHMKM",
    "items": [{"_id": "TG", "name": "Tủ gỗ nhỏ", "num": 1, "price": 450000}]
  }
  ```

- **Cấu trúc phản hồi:** Mảng các đối tượng, mỗi đối tượng cho một dịch vụ trong `services` đầu vào. Ví dụ:
  ```json
  [
    {
      "service_id": "SGN-BIKE",
      "data": {
        "distance": 11.98,
        "duration": 3240,
        "distance_fee": 69000,
        "request_fee": 17000,
        "stop_fee": 0,
        "vat_fee": 0,
        "discount": 0,
        "total_fee": 86000,
        "requests": [
          {"_id": "SGN-BIKE-TIP", "num": 1, "price": 5000},
          {"_id": "SGN-BIKE-BULKY", "num": 0, "price": 10000, "tier_code": "TIER_2"}
        ],
        "total_price": 86000
      }
    },
    {
      "service_id": "SGN-ECO",
      "data": {
        "distance": 11.98,
        "duration": 3240,
        "distance_fee": 60000,
        "request_fee": 0,
        "stop_fee": 0,
        "vat_fee": 0,
        "discount": 0,
        "total_fee": 60000,
        "total_price": 60000
      }
    }
  ]
  ```
  Trường chung trong `data`:  
  - `distance` (Number): Khoảng cách km.  
  - `duration` (Number): Thời gian giao (giây).  
  - `distance_fee`, `request_fee`, `stop_fee`, `vat_fee`, `discount`, `total_fee`, `total_price` (Number): Các khoản phí tương ứng ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%5B%20%7B%20%22service_id%22%3A%20%22SGN,0)) ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=%7B%20%22service_id%22%3A%20%22SGN,0)).  
  - `requests`: Chi tiết phí từ mỗi yêu cầu (tương tự `requests` ban đầu nhưng với giá `price`).  
- **Lưu ý:**  
  - So sánh nhiều dịch vụ song song, không thực sự tạo đơn.  
  - **Lỗi thường gặp:** 400 (`MISSING_REQUIRED_INFO`, `MISSING_PATH_INFO`, ...), 401 (`NOT_AUTHORIZED`: token sai) ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=Code%20Text%20Description%20,Service%20not%20found)), 404 (`SERVICE_NOT_FOUND`, `REQUEST_NOT_FOUND`), 406 (`INVALID_MAX_DISTANCE`, `INVALID_MAX_COD`), 500, 503 ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=Code%20Text%20Description%20,Special%20request%20not%20found)).

### Tạo đơn hàng (Create Order)

- **Phương thức / Endpoint:** `POST https://partner-apistg.ahamove.com/v3/orders`
- **Mô tả:** Tạo đơn hàng mới với một hoặc nhiều điểm giao. Có hai cách cung cấp dịch vụ: dùng `service_id` + `requests` hoặc `group_service_id` + `group_requests`. (Tài liệu chủ yếu ví dụ với `service_id`.)  
- **Xác thực:** Header `Content-Type: application/json` và `Authorization: Bearer <token>` của người tạo đơn ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Parameter%20Value%20Required%20Description%20Content,Token%20of%20the%20order%20creator)).  
- **Thân (Body):** JSON tương tự **Estimate Order**, gồm:
  - `order_time` (Number): 0 (giao ngay) hoặc timestamp đặt trước ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=order_time%20Float%20,TIP%2C%20BULKY%2C%20D2D)).
  - `path` (Array): Thông tin điểm pickup và drop, ít nhất 2 điểm ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,Th%C3%A0nh%20Th%C3%A1i%2C%20Qu%E1%BA%ADn%2010)). Mỗi điểm gồm `lat`, `lng`, `address` (bắt buộc), có thể thêm `short_address`, `name`, `mobile`, `cod`, `item_value`, `tracking_number`, `remarks`, `require_pod`, `require_verification` ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,%7D%2C)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Path)).
  - **Cách 1:** 
    - `service_id` (String, Bắt buộc): Mã dịch vụ cụ thể (ví dụ `SGN-BIKE`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%22service_id%22%3A%20%22SGN,BULKY%22%2C%20%22tier_code%22%3A%20%22TIER_2)).
    - `requests` (Array, Có): Danh sách yêu cầu đặc biệt (`_id`, `num` hoặc `tier_code` như đã nêu) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%22requests%22%3A%20%5B%20%7B%20%22_id%22%3A%20%22SGN,BULKY%22%2C%20%22tier_code%22%3A%20%22TIER_2%22)).
  - **Cách 2:** (Không bắt buộc, nếu dùng group)
    - `group_service_id` (String): Mã nhóm dịch vụ (ví dụ `BIKE`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=service_id%20String%20Yes%20Service%20code,TIP%2C%20BULKY%2C%20D2D)).
    - `group_requests` (Array): Danh sách `group_id` của yêu cầu (ví dụ `TIP`, `BULKY`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=service_id%20String%20Yes%20Service%20code,TIP%2C%20BULKY%2C%20D2D)).
    - Có thể dùng thay thế cho `service_id` & `requests`. Cấu trúc mã dịch vụ như `SGN-BIKE`, tách ra thành `city_id + group_service_id` ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=)).  
  - `payment_method` (String, Có): Phương thức thanh toán (`BALANCE`, `CASH`, `CASH_BY_RECIPIENT`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=payment_method%20String%20Yes%20Payment%20method,Cash%20payment%20by%20the%20recipient)).
  - `promo_code` (String, Không): Mã khuyến mãi nếu có ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=promo_code%20String%20No%20Promo%20code,Array%20No%20Package%20size%20specifications)).
  - `remarks` (String, Không): Ghi chú chung cho đơn ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=promo_code%20String%20No%20Promo%20code,Array%20No%20Package%20size%20specifications)).
  - `items` (Array, Không): Danh sách hàng hoá như trong Estimate (có `_id`, `name`, `price`, `num`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,GDB)).
  - `package_detail` (Array, Không): Thông số đóng gói (trọng lượng, kích thước) cho đơn vận chuyển vật cồng kềnh ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,%7D)).
  - `route_optimized` (Boolean, Không): `true` nếu muốn AhaMove tối ưu lộ trình (đơn có nhiều điểm giao).
- **Ví dụ Body (gồm `service_id`):** (xem [42†L400-L409], [42†L427-L436])
  ```json
  {
    "order_time": 0,
    "path": [
      {"lat": 10.76975, "lng": 106.66366, "address": "Địa chỉ A", "name": "Lan", "mobile": "84944309348"},
      {"lat": 10.80184, "lng": 106.71446, "address": "Địa chỉ B", "name": "Anh", "mobile": "0912345678", "cod": 100000, "tracking_number": "ABCD1234"}
    ],
    "service_id": "SGN-BIKE",
    "requests": [
      {"_id": "SGN-BIKE-TIP", "num": 1},
      {"_id": "SGN-BIKE-BULKY", "tier_code": "TIER_2"}
    ],
    "payment_method": "CASH",
    "remarks": "Ghi chú đơn",
    "promo_code": "AHMKM",
    "items": [
      {"_id": "TG", "name": "Tủ gỗ nhỏ", "num": 1, "price": 450000}
    ],
    "package_detail": [
      {"weight": 10, "length": 1.2, "width": 0.8, "height": 2.0, "description": "Nội thất"}
    ]
  }
  ```

- **Cấu trúc phản hồi:**  
  Trả về JSON gồm: 
  - `order_id` (String): Mã đơn hàng vừa tạo (ví dụ `"24ABCD"`) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,VND)).  
  - `status` (String): Trạng thái ban đầu (`ASSIGNING` nếu chờ giao cho tài xế) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,VND)).  
  - `shared_link` (String): URL rút gọn để người dùng theo dõi đơn hàng trên trình duyệt ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,VND)).  
  - `order` (Object): Chi tiết đơn hàng vừa tạo (có cấu trúc như **Data Model** bên trên), gồm các trường như `_id`, `currency`, `total_pay`, `distance`, `distance_fee`, `request_fee`, `total_fee`, `remarks`, `service_id`, `city_id`, `user_id`, `user_name`, `create_time`, `order_time`, `status`, `partner`, `polylines`, `special_request_price`, `distance_price`, `total_price`, v.v ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,0)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,33000%20...%20%7D)). Ví dụ (cắt ngắn):
  ```json
  {
    "order_id": "24ABCD",
    "status": "ASSIGNING",
    "shared_link": "https://expressstg.ahamove.com/s/241018N83HHH",
    "order": {
      "_id": "24ABCD",
      "currency": "VND",
      "user_main_account": 0,
      "user_bonus_account": 0,
      "total_pay": 33000,
      "distance": 1.02,
      "duration": 268,
      "distance_fee": 23000,
      "request_fee": 10000,
      "stop_fee": 0,
      "vat_fee": 0,
      "discount": 0,
      "path": [ ... ],
      "requests": [...],
      "total_fee": 33000,
      "remarks": "Ghi chú đơn hàng",
      "service_id": "SGN-BIKE",
      "city_id": "SGN",
      "user_id": "84xxxxxxxxx",
      "user_name": "TEST CREATE AN ORDER",
      "create_time": **********.2757928,
      "order_time": **********.2757928,
      "status": "ASSIGNING",
      ...
      "stoppoint_price": 0,
      "special_request_price": 10000,
      "distance_price": 23000,
      "total_price": 33000
    }
  }
  ```
  (Xem chi tiết trong tài liệu ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=%7B%20,0)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,33000%20...%20%7D)).)
- **Lưu ý:**  
  - Phải tuân thủ quy tắc mã dịch vụ hoặc group. Trường hợp dùng `group_service_id`/`group_requests`, tham khảo cấu trúc mã ở phần *How to get group_service_id and group_requests* ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,group_requests)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Example%3A)).  
  - `address` trong `path` phải định dạng đầy đủ và rõ ràng (xem lưu ý về định dạng địa chỉ ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Note))).  
  - **Lỗi thường gặp:** 400 (`BAD_REQUEST`, `MISSING_REQUIRED_INFO`, `MISSING_PATH_INFO`), 401 (`NOT_AUTHORIZED`: token sai) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Code%20Text%20Description%20,Special%20request%20not%20found)), 404 (`SERVICE_NOT_FOUND`, `REQUEST_NOT_FOUND`), 406 (`INVALID_MAX_DISTANCE`, `INVALID_MAX_COD`, `NOT_ENOUGH_CREDIT`, `INVALID_MAX_STOP_POINT`, `INVALID_MIN_STOP_POINT`, `SERVICE_NOT_VALID_AT_PICKUP`, `INVALID_PICKUP_AREA`, `INVALID_DELIVERY_AREA`, `INVALID_SERVICE_HOUR`, `INVALID_FUTURE_ORDER_TIME`), 409 (`DUPLICATE_TRACKING_NUMBER`), 500, 503 ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Code%20Text%20Description%20,allowed%20distance%20of%20the%20service)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=,Try%20again%20later)).

### Hủy đơn hàng (Cancel Order)

Cung cấp hai cách để hủy:

- **Hủy theo Order ID:**  
  - **Phương thức / Endpoint:** `DELETE https://partner-apistg.ahamove.com/v3/orders/<order_id>` ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=HTTP%20Request)).  
  - **Xác thực:** Header `Content-Type: application/json` và `Authorization: Bearer <token>` (token người tạo đơn).  
  - **Tham số:** Trong URL là `<order_id>` (Mã đơn Ahamove) và **Query** `order_id` (duplicate) và `comment` (Lý do hủy, bắt buộc nếu đơn ở trạng thái `ACCEPTED`) ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=Query%20Parameters)).  
  - **Phản hồi:** `{}` nếu thành công (HTTP 200).  
  - **Lỗi:** 401 (`NOT_AUTHORIZED`), 404 (`ORDER_NOT_FOUND`), 406 (`INVALID_ORDER_STATUS`), 500, 503 ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=Errors)).

- **Hủy theo Tracking Number:** (áp dụng cho đơn có 1 pickup – N delivery)  
  - **Phương thức / Endpoint:** `DELETE https://partner-apistg.ahamove.com/v3/orders/tracks` ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=HTTP%20Request)).  
  - **Xác thực:** Header `Content-Type: application/json` và `Authorization: Bearer <token>`.  
  - **Tham số:** Body JSON gồm:
    - `tracking_number` (String, Có): Mã tracking của một điểm giao ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=Query%20Parameters)).
    - `comment` (String, Có): Lý do hủy (nếu đơn ở trạng thái `ACCEPTED`).  
  - **Phản hồi:** `{}` nếu thành công (HTTP 200) ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=Response)).  
  - **Lỗi:** 401 (`NOT_AUTHORIZED`: không có quyền hủy đơn), 404 (`ORDER_NOT_FOUND`), 406 (`INVALID_ORDER_STATUS`), 500, 503 ([Cancel Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/cancel-order#:~:text=Errors)).

### Xem chi tiết đơn hàng (Get Order Detail)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/orders/<order_id>`
- **Mô tả:** Lấy chi tiết đơn hàng (như trong **Data Model**) cho `order_id` đã tạo.  
- **Xác thực:** Header `Authorization: Bearer <token>` của người tạo đơn (hoặc tài khoản cha).  
- **Tham số truy vấn:**  
  | Tên tham số | Kiểu   | Bắt buộc | Mô tả           |
  |-------------|--------|----------|-----------------|
  | `order_id`  | String | Có       | Mã đơn hàng cần lấy ([Get Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-detail#:~:text=Query%20Parameters)). |

- **Cấu trúc phản hồi:** Trả về đối tượng JSON chi tiết của đơn (giống **Data Model**), gồm tất cả các trường (ID, trạng thái, `path`, `requests`, chi phí, v.v.) ([Get Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-detail#:~:text=Response)). Ví dụ rút gọn:
  ```json
  {
    "_id": "24ABCD",
    ...
    "status": "ASSIGNING",
    "path": [...],
    "total_price": 33000
  }
  ```
- **Lưu ý:**  
  - Token phải có quyền (thuộc tài khoản tạo đơn hoặc tài khoản cha).  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: token không hợp lệ), 403 (`DENY_ACCESS_ORDER_DETAIL`: không có quyền xem), 404 (`ORDER_NOT_FOUND`), 500, 503 ([Get Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-detail#:~:text=Code%20Text%20Description%20,Try%20again%20later)).

### Xem chi tiết nhiều đơn (Get Multiple Order Detail)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/orders?ids=<order_id>,<order_id>,...`
- **Mô tả:** Lấy thông tin chi tiết cho nhiều `order_id` cùng lúc.  
- **Xác thực:** Header `Authorization: Bearer <token>` của người tạo đơn (hoặc tài khoản cha).  
- **Tham số truy vấn:**  
  | Tên tham số | Kiểu   | Bắt buộc | Mô tả                                      |
  |-------------|--------|----------|--------------------------------------------|
  | `ids`       | String | Có       | Danh sách các `order_id` phân cách bằng dấu phẩy ([Get Multiple Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-multiple-orders-detail#:~:text=Query%20Parameters)). |

- **Cấu trúc phản hồi:** Đối tượng JSON chứa các đơn (mỗi thứ tự tương ứng), tương tự Get Order Detail. Ví dụ:
  ```json
  {
    { "_id": "24ABCD", ... },
    { "_id": "24EFGH", ... },
    { "_id": "24IJKL", ... }
  }
  ```
  (Mỗi đối tượng lồng tương tự **Data Model** ([Get Multiple Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-multiple-orders-detail#:~:text=)).)  
- **Lưu ý:**  
  - **Lỗi thường gặp:** Tương tự Get Order Detail (401, 403, 404, 500, 503) ([Get Multiple Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-multiple-orders-detail#:~:text=Code%20Text%20Description%20,Try%20again%20later)).

### Lấy danh sách order theo tracking (Get Order List By Tracking Number)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/orders/tracks?tracking_number=<tracking_number>`
- **Mô tả:** Cho 1 mã tracking (nội bộ của đối tác), trả về danh sách các `order_id` Ahamove có chứa mã đó. (Áp dụng cho đơn 1–N điểm giao.)  
- **Xác thực:** Header `Authorization: Bearer <token>` của partner.  
- **Tham số truy vấn:**  
  | Tên tham số       | Kiểu   | Bắt buộc | Mô tả                              |
  |-------------------|--------|----------|------------------------------------|
  | `tracking_number` | String | Có       | Mã tracking nội bộ của đối tác.    |
  | `from_time`       | Int    | Không    | (Không bắt buộc) Thời điểm bắt đầu tìm (mặc định 7 ngày trước). |

- **Cấu trúc phản hồi:** Mảng các đối tượng đơn gồm các trường:
  - `_id` (String): Order ID AhaMove.  
  - `path` (Array): Các điểm giao (chỉ chứa `address` và nếu có, `status`, `tracking_number`) ([Get Order List By Tracking Number | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-list-by-tracking-number#:~:text=%5B%20%7B%20,Th%C3%A0nh%20ph%E1%BB%91%20H%E1%BB%93%20Ch%C3%AD%20Minh)).  
  - `status` (String): Trạng thái đơn hàng.  
  - `service_id` (String): Mã dịch vụ.  
  - `create_time` (Number): Thời gian tạo đơn (epoch) ([Get Order List By Tracking Number | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-list-by-tracking-number#:~:text=%5B%20%7B%20,Th%C3%A0nh%20ph%E1%BB%91%20H%E1%BB%93%20Ch%C3%AD%20Minh)).  

  Ví dụ:
  ```json
  [
    {
      "_id": "24AV9K67",
      "path": [
        {"address": "Địa chỉ A"},
        {"address": "Địa chỉ B", "status": "FAILED", "tracking_number": "200207"}
      ],
      "status": "COMPLETED",
      "service_id": "SGN-BIKE",
      "create_time": 1729502059
    },
    {
      "_id": "247D21C5",
      "path": [
        {"address": "Địa chỉ A"},
        {"address": "Địa chỉ B", "tracking_number": "200207"}
      ],
      "status": "CANCELLED",
      "service_id": "SGN-BIKE",
      "create_time": 1729497661
    }
  ]
  ```
  (Ví dụ từ tài liệu ([Get Order List By Tracking Number | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-list-by-tracking-number#:~:text=%5B%20%7B%20,Th%C3%A0nh%20ph%E1%BB%91%20H%E1%BB%93%20Ch%C3%AD%20Minh)).)  
- **Lưu ý:**  
  - Kết quả gồm cả đơn đã hoàn thành hoặc hủy.  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`), 404 (`ORDER_NOT_FOUND` nếu không tìm thấy), 500, 503 ([Get Order List By Tracking Number | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-list-by-tracking-number#:~:text=Code%20Text%20Description%20,Try%20again%20later)).

### Lấy link theo dõi đơn (Get Order Tracking Link)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/orders/<order_id>/shared-link`
- **Mô tả:** Lấy liên kết chia sẻ (shared link) để người dùng cuối theo dõi tình trạng đơn hàng.  
- **Xác thực:** Header `Authorization: Bearer <token>` (token partner).  
- **Tham số truy vấn:**

  | Tên tham số | Kiểu   | Bắt buộc | Mô tả           |
  |-------------|--------|----------|-----------------|
  | `order_id`  | String | Có       | Mã đơn hàng.    |

- **Cấu trúc phản hồi:** Trả về JSON với `shared_link` (String) là URL theo dõi đơn hàng ([Get Order Tracking Link | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-tracking-link#:~:text=%7B%20)). Ví dụ: `{"shared_link": "https://expressstg.ahamove.com/s/241021HRHVJ4"}`.
- **Lưu ý:**  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`: không có quyền), 404 (`ORDER_NOT_FOUND`), 500, 503 ([Get Order Tracking Link | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-tracking-link#:~:text=Code%20Text%20Description%20,Try%20again%20later)).

### Nhật ký Callback đơn hàng (Get Order Callback Logs)

- **Phương thức / Endpoint:** `GET https://partner-apistg.ahamove.com/v3/orders/callback-logs?tracking_number=<tracking_number>`
- **Mô tả:** Trả về lịch sử các lần Ahamove gọi webhook (callback) cập nhật trạng thái đơn hàng của một mã tracking.  
- **Xác thực:** Header `Authorization: Bearer <token>` (token partner).  
- **Tham số truy vấn:**

  | Tên tham số       | Kiểu   | Bắt buộc | Mô tả                                 |
  |-------------------|--------|----------|---------------------------------------|
  | `tracking_number` | String | Có       | Mã tracking nội bộ đối tác.           |

- **Cấu trúc phản hồi:** Đối tượng JSON gồm:
  - `tracking_number` (String): Mã tracking.  
  - `callback_logs` (Array): Danh sách log, mỗi mục gồm:
    - `partner` (String): Tên partner (gửi callback).  
    - `response_code` (Number): Mã HTTP Ahamove trả về sau callback (ví dụ 200).  
    - `response_time` (Number): Thời gian nhận phản hồi (epoch) ([Get Order Callback Logs | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-callback-logs#:~:text=,)).
    - `callback_time` (Number): Thời điểm Ahamove gọi callback (epoch).  
    - `request` (Object): Thông tin đơn hàng gốc (giống **Data Model** / Get Order Detail) ([Get Order Callback Logs | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-callback-logs#:~:text=,%2F%2Forder%20detail%20%7D)).  
    - `response_text` (String): Nội dung phản hồi (nếu có).  
  Ví dụ:
  ```json
  {
    "tracking_number": "HD0001",
    "callback_logs": [
      {
        "partner": "guu",
        "response_code": 200,
        "response_time": 1729486904.970746,
        "callback_time": 1729486904.0418425,
        "request": { "_id": "24ABCD", "status": "COMPLETED", ... }
      }
    ]
  }
  ```
  (Tham khảo [65†L112-L120] về định dạng.)
- **Lưu ý:**  
  - **Lỗi thường gặp:** 401 (`NOT_AUTHORIZED`), 500, 503 ([Get Order Callback Logs | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-callback-logs#:~:text=Errors)).

### Cập nhật đơn hàng (Update Order Information)

- **Phương thức / Endpoint:** `PATCH https://partner-apistg.ahamove.com/v3/orders/<order_id>`
- **Mô tả:** Cho phép cập nhật thông tin đơn sau khi đã tạo (trong một số điều kiện nhất định).  
- **Điều kiện:** Đơn ở trạng thái `IDLE`, `ASSIGNING` hoặc `ACCEPTED`.  
- **Cập nhật được:** Trong `path[x]`:
  - `name`, `mobile`, `remarks` (không giới hạn số lần) ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Parameter%20Update%20Limit%20Allowed%20Services,points%20in%20path%20BALANCE%2C%20CASH)).
  - `cod`, `address` (chỉ 1 lần, cho dịch vụ OnWheel) ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Parameter%20Update%20Limit%20Allowed%20Services,points%20in%20path%20BALANCE%2C%20CASH)).
- **Xác thực:** Header `Authorization: Bearer <token>` của người tạo đơn.  
- **Thân (Body):** JSON gồm:
  - `path_index` (Int): Chỉ số điểm trong `path` cần cập nhật (0 là điểm lấy hàng, các giá trị >=1 là điểm giao thứ tự) ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Parameter%20Type%20Required%20Description%20path_index,Address%20information%20for%20delivery%20points)).
  - Các trường muốn cập nhật tại điểm đó (tùy theo phần gạch đầu dòng ở trên) như `name`, `mobile`, `remarks`, `cod`, `address_info`.
  - `address_info` (Object, tùy chọn): Nếu cập nhật địa chỉ, gồm `address` (định dạng đầy đủ), có thể có `lat`, `lng` để rõ hơn ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Model%20c%E1%BB%A7a%20address_info)).
- **Ví dụ Body:** (Cập nhật điểm giao thứ 1)
  ```json
  {
    "path_index": 1,
    "name": "Anh",
    "mobile": "09xxxxxxxx",
    "remarks": "Cổng đón khách A",
    "cod": 220000,
    "address_info": {
      "lat": 10.798026,
      "lng": 106.696369,
      "address": "132 Lê Văn Duyệt, Bình Thạnh, TP.HCM"
    }
  }
  ```
  (Theo [68†L148-L157] và [68†L173-L182].)

- **Cấu trúc phản hồi:** `{}` nếu thành công (HTTP 200).  
- **Lưu ý:**  
  - Không thể thay đổi pickup point (`path_index = 0`) theo mặc định (ngoại trừ đối tác có cấu hình riêng).  
  - Cập nhật bị giới hạn tùy loại dịch vụ và trạng thái đơn (xem bảng hạn chế ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Parameter%20Update%20Limit%20Allowed%20Services,points%20in%20path%20BALANCE%2C%20CASH))).  
  - **Lỗi thường gặp:** 400 (`BAD_REQUEST`, `MISSING_REQUIRED_INFO`, `INVALID_DATA`), 401 (`NOT_AUTHORIZED`: token sai), 403 (`ORDER_STATUS_NOT_ALLOWED` nếu trạng thái không cho phép), 404 (`ORDER_NOT_FOUND`), 406 (`NOT_ALLOW_ORDER_WITH_PROMO_CODE`, `NOT_ALLOW_CHANGE_PICKUP_ADDRESS`, `INVALID_PAYMENT_METHOD`), 500, 503 ([Update Order Information | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/update-order-path#:~:text=Errors)).

### Đánh giá tài xế (Rate A Supplier)

- **Phương thức / Endpoint:** `PUT https://partner-apistg.ahamove.com/v3/orders/<order_id>/rate`
- **Mô tả:** Đánh giá tài xế sau khi đơn hoàn thành.  
- **Xác thực:** Header `Content-Type: application/json` và `Authorization: Bearer <token>` của người tạo đơn.  
- **Thân (Body):** JSON với:
  - `rated_by` (String, Có): Người đánh giá: `"user"` (người gửi) hoặc `"receiver"` (người nhận) ([Rate A Supplier | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/rate-a-supplier#:~:text=%7B%20,)).
  - `rating` (Int, Có): Điểm đánh giá (1–5) ([Rate A Supplier | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/rate-a-supplier#:~:text=%7B%20,)).
  - `comment` (String, Không): Bình luận (không bắt buộc).  
  (Các tham số trên cũng được truyền như query parameters tùy API trợ giúp, nhưng thực chất có thể dùng trong body như ví dụ.)

- **Cấu trúc phản hồi:** `{}` (HTTP 200) nếu thành công ([Rate A Supplier | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/rate-a-supplier#:~:text=)).  
- **Lưu ý:**  
  - Mỗi đơn tài xế có thể được đánh giá một lần bởi người gửi và một lần bởi người nhận.  
  - **Lỗi thường gặp:** 400 (`MISSING_REQUIRED_INFO`, `INVALID_PARAM`), 401 (`NOT_AUTHORIZED`), 404 (`SUPPLIER_NOT_FOUND`: không tìm thấy tài xế), 409 (`ORDER_HAS_BEEN_RATED_BY_USER`/`_RECEIVER`: đã đánh giá rồi), 500, 503 ([Rate A Supplier | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/rate-a-supplier#:~:text=Errors)).

## Webhook & Callback

- **Cài đặt Webhook:** Đối tác cung cấp một URL webhook để Ahamove gửi sự kiện khi đơn hàng thay đổi trạng thái ([Webhook & Callback flow | Ahamove Developers](https://developers.ahamove.com/en/docs/webhook#:~:text=Partners%20will%20need%20to%20provide,time%20the%20order%20has%20updates)). Mỗi lần đơn có cập nhật (chấp nhận, lấy hàng, giao thành công, hủy, v.v.), Ahamove sẽ gọi đến URL này kèm dữ liệu `order_id` và `status`.  
- **Kiểm thử:** Để kiểm thử webhook, có thể dùng app giả lập tài xế (AhaDriver STG) theo hướng dẫn ([Webhook & Callback flow | Ahamove Developers](https://developers.ahamove.com/en/docs/webhook#:~:text=How%20to%20test%20callback%20and,Webhook)).
- **Xác thực yêu cầu:** Ahamove hỗ trợ 3 cơ chế xác thực (điền header):
  - `API-KEY`: Đặt header `{apikey: "<api_key>"}` ([Webhook & Callback flow | Ahamove Developers](https://developers.ahamove.com/en/docs/webhook#:~:text=)).
  - `Bearer Token`: Đặt header `Authorization: Bearer <token>` ([Webhook & Callback flow | Ahamove Developers](https://developers.ahamove.com/en/docs/webhook#:~:text=)).
  - `Basic Auth`: Đặt header `Authorization: Basic <base64(username:password)>` ([Webhook & Callback flow | Ahamove Developers](https://developers.ahamove.com/en/docs/webhook#:~:text=)).
- **Định dạng dữ liệu:** Callback gửi JSON với `order_id` và `status`. Đối tác nên trả về HTTP 200 OK để báo đã nhận thành công.  
- **Lưu ý:** Địa chỉ webhook phải truy cập được từ internet (ưu tiên HTTPS) và xử lý đúng JSON theo định dạng của Ahamove.

## Các lưu ý chung

- **Xác thực:** Hầu hết API yêu cầu token (header `Authorization: Bearer <token>`) trừ khi ghi khác. Token lấy được từ **Register** (đăng ký) và **Authenticate token**. Đối với các API Tài khoản, `api_key` và `mobile` dùng để lấy token ([Register Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/register-account#:~:text=Parameter%20Type%20Required%20Description%20api_key,after%20Partner%20registered%20for%20integration)) ([Authenticate token | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/authenticate-token#:~:text=Parameter%20Type%20Required%20Description%20api_key,number%20registered%20to%20the%20account)).  
- **Giới hạn (Rate Limit):** Tài liệu chính thức không đề cập rõ giới hạn rate limit. Đối tác nên sử dụng hợp lý, không spam quá mức.  
- **Phiên bản API:** Tài liệu này dựa trên phiên bản 3.0.0 cập nhật đến Dec 18, 2024.  
- **Lỗi chung:** Ngoài các lỗi đã liệt kê, các lỗi phổ biến như `400 Bad Request`, `401 Unauthorized`, `500 Internal Server Error`, `503 Service Unavailable` có thể xảy ra do dữ liệu sai hoặc sự cố server. Luôn kiểm tra mã lỗi và thông điệp trả về theo tài liệu.
- **Các object phụ:** Trong các phản hồi, các đối tượng con (như mục `path` trong đơn, đối tượng trong `requests`, `items`, v.v.) được mô tả phần lớn trong **Data Model** trên. Đối tác cần tuân thủ cấu trúc dữ liệu này khi xử lý.

**Tài liệu tham khảo:** Thông tin trên được tổng hợp từ tài liệu chính thức của Ahamove Developers (API Reference) ([Register Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/register-account#:~:text=Parameter%20Type%20Required%20Description%20api_key,after%20Partner%20registered%20for%20integration)) ([Add Child Account | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/account-apis/add-child-account#:~:text=Query%20Parameters)) ([Get Service Details | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/master-data/get-service-detail#:~:text=%5B%20%7B%20%22_id%22%3A%20%22SGN,0.212)) ([Estimate Order Fee | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/estimate-order-fee#:~:text=Response)) ([Create Order | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/create-order#:~:text=Code%20Text%20Description%20,is%20invalid%2C%20exceeding%20the%20allowed)) ([Get Order Detail | Ahamove Developers](https://developers.ahamove.com/en/docs/api-reference/order-apis/get-order-detail#:~:text=Code%20Text%20Description%20,Try%20again%20later)).